// 主JS文件
document.addEventListener("DOMContentLoaded", function () {
  // 更新顶部状态栏时间
  updateStatusBarTime();
  setInterval(updateStatusBarTime, 60000);

  // 绑定导航事件
  setupNavigation();

  // 添加页面过渡动画
  addPageTransitions();
});

// 更新状态栏时间
function updateStatusBarTime() {
  const timeElement = document.querySelector(".status-bar-time");
  if (timeElement) {
    const now = new Date();
    const hours = now.getHours().toString().padStart(2, "0");
    const minutes = now.getMinutes().toString().padStart(2, "0");
    timeElement.textContent = `${hours}:${minutes}`;
  }
}

// 设置导航
function setupNavigation() {
  const navItems = document.querySelectorAll(".nav-item");

  navItems.forEach((item) => {
    item.addEventListener("click", function () {
      // 移除其他导航项目的active类
      navItems.forEach((nav) => nav.classList.remove("active"));

      // 添加当前项目的active类
      this.classList.add("active");

      // 切换页面（如果需要）
      const target = this.getAttribute("data-target");
      if (target) {
        navigateTo(target);
      }
    });
  });
}

// 页面导航
function navigateTo(pageName) {
  const iframe = document.querySelector("#content-frame");
  if (iframe) {
    if (pageName === "home" || pageName === "profile") {
      iframe.src = `pages/${pageName}.html`;
    } else {
      console.log("页面不存在");
    }
  }
}

// 添加页面过渡动画
function addPageTransitions() {
  const contentElements = document.querySelectorAll(".fade-in, .slide-up");

  contentElements.forEach((element) => {
    // 确保元素初始不可见
    element.style.opacity = "0";

    // 使用IntersectionObserver检测元素何时进入视口
    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          // 元素进入视口时，应用动画
          entry.target.style.opacity = "1";
          observer.unobserve(entry.target);
        }
      });
    });

    observer.observe(element);
  });
}

// 表单验证
function validateForm(formId) {
  const form = document.getElementById(formId);
  if (!form) return false;

  let isValid = true;
  const inputs = form.querySelectorAll("input, textarea, select");

  inputs.forEach((input) => {
    if (input.hasAttribute("required") && !input.value.trim()) {
      isValid = false;
      input.classList.add("error");
    } else {
      input.classList.remove("error");
    }
  });

  return isValid;
}

// 图片预览
function previewImage(input, previewId) {
  const preview = document.getElementById(previewId);
  if (!preview) return;

  if (input.files && input.files[0]) {
    const reader = new FileReader();

    reader.onload = function (e) {
      preview.src = e.target.result;
      preview.style.display = "block";
    };

    reader.readAsDataURL(input.files[0]);
  }
}

// 模拟API调用
function simulateApiCall(endpoint, data = {}, successCallback, errorCallback) {
  // 模拟网络延迟
  setTimeout(() => {
    // 模拟90%的成功率
    if (Math.random() < 0.9) {
      if (successCallback) successCallback({ status: "success", data: data });
    } else {
      if (errorCallback)
        errorCallback({ status: "error", message: "网络错误，请重试" });
    }
  }, 800);
}

// 显示提示
function showToast(message, type = "info") {
  const toast = document.createElement("div");
  toast.className = `toast toast-${type} fade-in`;
  toast.textContent = message;

  document.body.appendChild(toast);

  // 2秒后自动消失
  setTimeout(() => {
    toast.classList.add("fade-out");
    setTimeout(() => {
      document.body.removeChild(toast);
    }, 300);
  }, 2000);
}

// 多语言支持 (简单实现)
const translations = {
  zh: {
    submit: "提交",
    cancel: "取消",
    login: "登录",
    register: "注册",
    home: "首页",
    profile: "我的",
    // 更多翻译...
  },
  en: {
    submit: "Submit",
    cancel: "Cancel",
    login: "Login",
    register: "Register",
    home: "Home",
    profile: "Profile",
    // 更多翻译...
  },
};

let currentLang = "zh";

function setLanguage(lang) {
  if (!translations[lang]) return;

  currentLang = lang;

  document.querySelectorAll("[data-i18n]").forEach((element) => {
    const key = element.getAttribute("data-i18n");
    if (translations[lang][key]) {
      element.textContent = translations[lang][key];
    }
  });
}

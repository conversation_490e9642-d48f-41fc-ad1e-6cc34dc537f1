/* 响应式样式 */

/* 手机容器响应式调整 */
@media (max-width: 414px) {
  .phone-container {
    width: 100%;
    max-width: 350px;
    height: 700px;
    border-width: 8px;
    border-radius: 30px;
    margin: 15px auto;
  }
}

@media (max-width: 320px) {
  .phone-container {
    height: 600px;
    border-width: 6px;
    border-radius: 25px;
    margin: 10px auto;
  }
}

/* 表单元素通用修复 - 提高选择器特异性 */
html body .form-group,
html body .form-input,
html body .form-textarea,
html body .form-select,
html body .search-input,
html body select,
html body textarea,
html body .input-with-icon,
html body .verification-code,
html body .tag-input,
html body .tag-container {
  width: 100% !important;
  max-width: 100% !important;
  box-sizing: border-box !important;
}

/* 验证码按钮固定宽度 */
.get-code-btn {
  flex: 0 0 auto !important;
  min-width: 85px !important;
  width: auto !important;
  white-space: nowrap !important;
}

/* 确保表单内的嵌套容器不溢出 */
html body .form-section,
html body .form-container,
html body .login-form-container,
html body .register-form-container,
html body .upload-section {
  width: 100% !important;
  max-width: 100% !important;
  padding-left: 15px !important;
  padding-right: 15px !important;
  box-sizing: border-box !important;
}

/* 修复所有页面表单布局 */
@media (max-width: 414px) {
  .form-row {
    flex-direction: column !important;
    gap: 10px !important;
  }

  .form-row .form-group {
    width: 100% !important;
  }

  .tag-container {
    flex-wrap: wrap !important;
  }

  .image-upload-container {
    gap: 5px !important;
  }

  /* 缩小输入框内边距 */
  input,
  select,
  textarea,
  .form-control {
    padding: 10px 12px !important;
  }

  .input-with-icon .form-control,
  .input-with-icon input {
    padding-left: 40px !important;
  }
}

/* 小型手机 - 320px以下 */
@media (max-width: 320px) {
  .app-content {
    font-size: 14px;
  }

  .bottom-nav {
    height: 70px;
  }

  .nav-item i {
    font-size: 18px;
  }

  .work-card .work-info {
    padding: 8px;
  }

  .form-input,
  .form-textarea,
  .form-select,
  input,
  select,
  textarea {
    font-size: 14px;
    padding: 10px 12px;
  }

  .action-buttons .action-btn {
    font-size: 14px;
    padding: 12px;
  }

  /* 缩小内边距，防止溢出 */
  .upload-section,
  .detail-section,
  .form-section {
    padding: 0 10px;
  }
}

/* 中型手机 - 375px左右 */
@media (min-width: 321px) and (max-width: 375px) {
  .works-container {
    gap: 10px;
    padding: 10px;
  }

  .work-image {
    padding-top: 130%;
  }

  .filter-drawer {
    padding: 15px;
  }

  .preview-container {
    grid-template-columns: repeat(3, 1fr);
    gap: 8px;
  }
}

/* 大型手机 - 414px及以上 */
@media (min-width: 376px) {
  .work-card .work-info {
    padding: 15px;
  }

  .work-title {
    font-size: 15px;
  }

  .preview-container {
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
  }
}

/* 通用修复 */
@media (max-width: 414px) {
  /* 固定底部元素位置，确保不与固定的导航栏重叠 */
  .action-bar,
  .filter-button {
    bottom: 90px !important;
    position: fixed;
    z-index: 99;
  }

  /* 增加底部内容padding，防止被遮挡 */
  .work-detail,
  .upload-page,
  .profile-page,
  .app-content {
    padding-bottom: 100px !important;
  }

  /* 确保弹出层不超出屏幕 */
  .filter-drawer {
    max-height: 70vh;
    overflow-y: auto;
    z-index: 101; /* 确保在底部导航栏之上 */
  }

  /* 修复底部导航栏在小屏幕上的样式 */
  .bottom-nav {
    padding: 0 5px;
  }

  .nav-item {
    width: 50px;
  }

  /* 优化表单在小屏幕上的显示 */
  .form-group {
    margin-bottom: 15px;
  }

  .form-label {
    margin-bottom: 5px;
  }
}

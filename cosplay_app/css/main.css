/* 主样式文件 */
@import url("https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap");

/* 全局盒模型设置，确保所有元素包括伪元素都使用border-box */
*,
*::before,
*::after {
  box-sizing: border-box;
}

/* 专门为表单元素设置盒模型，修复浏览器默认样式问题 */
input,
input[type="text"],
input[type="password"],
input[type="email"],
input[type="number"],
input[type="tel"],
input[type="url"],
input[type="search"],
input[type="date"],
input[type="time"],
input[type="datetime-local"],
input[type="month"],
input[type="week"],
textarea,
select,
button {
  box-sizing: border-box !important;
  max-width: 100% !important;
}

:root {
  --primary: #8a2be2;
  --primary-light: #9d4edd;
  --secondary: #ffd166;
  --dark: #2d3748;
  --light: #f7fafc;
  --danger: #e53e3e;
  --success: #38a169;
  --warning: #dd6b20;
  --gray-100: #f7fafc;
  --gray-200: #edf2f7;
  --gray-300: #e2e8f0;
  --gray-400: #cbd5e0;
  --gray-500: #a0aec0;
  --gray-600: #718096;
  --gray-700: #4a5568;
  --gray-800: #2d3748;
  --gray-900: #1a202c;
}

body {
  font-family: "Noto Sans SC", sans-serif;
  margin: 0;
  padding: 0;
  background-color: #f5f5f5;
  color: var(--gray-800);
}

.phone-container {
  width: 390px;
  height: 844px;
  background: #fff;
  border-radius: 40px;
  overflow: hidden;
  position: relative;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  margin: 20px auto;
  border: 10px solid #000;
  display: block;
  box-sizing: border-box;
}

.status-bar {
  height: 44px;
  background-color: #000;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  color: white;
  font-size: 12px;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  width: 100%;
}

.status-bar-notch {
  position: absolute;
  width: 150px;
  height: 30px;
  background-color: #000;
  border-radius: 0 0 20px 20px;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
}

.app-content {
  height: calc(100% - 44px - 80px);
  overflow-y: auto;
  background-color: var(--gray-100);
  padding-bottom: 80px;
  padding-top: 44px;
}

.bottom-nav {
  height: 80px;
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-top: 1px solid var(--gray-200);
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 0 10px;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 100;
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--gray-600);
  font-size: 10px;
  width: 60px;
  height: 60px;
}

.nav-item.active {
  color: var(--primary);
}

.nav-item i {
  font-size: 22px;
  margin-bottom: 5px;
}

/* 组件样式 */
.card {
  background: #fff;
  border-radius: 16px;
  overflow: hidden;
  margin: 10px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.glass-card {
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  overflow: hidden;
  margin: 10px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn {
  display: inline-block;
  padding: 12px 24px;
  border-radius: 12px;
  font-weight: 500;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s;
  border: none;
  font-size: 16px;
}

.btn-primary {
  background-color: var(--primary);
  color: white;
  box-shadow: 0 4px 12px rgba(138, 43, 226, 0.3);
}

.btn-primary:hover {
  background-color: var(--primary-light);
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(138, 43, 226, 0.4);
}

.btn-secondary {
  background-color: white;
  color: var(--primary);
  border: 1px solid var(--primary);
}

.btn-block {
  display: block;
  width: 100%;
}

/* 表单样式 */
.form-group {
  margin-bottom: 20px;
  width: 100%;
  max-width: 100%;
}

.form-control {
  width: 100%;
  padding: 14px 16px;
  border-radius: 12px;
  border: 1px solid var(--gray-300);
  background: white;
  font-size: 16px;
  transition: all 0.2s;
  max-width: 100%;
  box-sizing: border-box;
}

.form-control:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(138, 43, 226, 0.2);
}

.form-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  font-size: 14px;
  color: var(--gray-700);
}

/* 添加输入框通用样式 */

textarea,
select {
  width: 100%;
  max-width: 100%;
  padding: 12px 15px;
  border-radius: 12px;
  border: 1px solid var(--gray-300);
  font-size: 15px;
  color: var(--gray-800);
  background: white;
  box-sizing: border-box;
}

textarea {
  min-height: 100px;
  resize: vertical;
}

/* 修复表单行在小屏幕上的显示 */
.form-row {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  width: 100%;
}

.form-row .form-group {
  flex: 1;
  min-width: 120px;
  width: 100%;
}

/* 修复带图标的输入框 */
.input-with-icon {
  position: relative;
  width: 100%;
  max-width: 100%;
}

.input-with-icon .form-control,
.input-with-icon input {
  padding-left: 45px;
  width: 100%;
  max-width: 100%;
}

.verification-code {
  display: flex;
  width: 100%;
  gap: 10px;
}

.verification-code .input-with-icon,
.verification-code .form-control {
  width: 100%;
  flex: 1;
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.slide-up {
  animation: slideUp 0.4s ease-in-out;
}

/* 通用工具类 */
.text-center {
  text-align: center;
}
.text-primary {
  color: var(--primary);
}
.bg-primary {
  background-color: var(--primary);
}
.flex {
  display: flex;
}
.items-center {
  align-items: center;
}
.justify-center {
  justify-content: center;
}
.justify-between {
  justify-content: space-between;
}
.mt-2 {
  margin-top: 0.5rem;
}
.mb-2 {
  margin-bottom: 0.5rem;
}
.mt-4 {
  margin-top: 1rem;
}
.mb-4 {
  margin-bottom: 1rem;
}
.p-4 {
  padding: 1rem;
}
.rounded-full {
  border-radius: 9999px;
}
.font-bold {
  font-weight: 700;
}
.text-sm {
  font-size: 0.875rem;
}
.text-lg {
  font-size: 1.125rem;
}
.text-xl {
  font-size: 1.25rem;
}
.text-2xl {
  font-size: 1.5rem;
}
.w-full {
  width: 100%;
}
.h-full {
  height: 100%;
}
.relative {
  position: relative;
}
.absolute {
  position: absolute;
}
.overflow-hidden {
  overflow: hidden;
}

/* 表单容器样式 */
.form-container,
.login-form-container,
.register-form-container,
.face-recog-content,
.upload-page,
.form-section,
.upload-section {
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
}

/* 确保表单元素的父容器不溢出 */
.search-bar,
.verification-code,
.input-with-icon,
.tag-container,
.form-row,
.radio-options,
.image-upload-container,
.venue-options,
.action-buttons {
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
}

/* 表单按钮和交互元素 */
.btn,
.action-btn,
.form-submit,
.register-btn,
.login-btn,
.continue-btn,
.get-code-btn,
.restart-btn {
  box-sizing: border-box;
}

/* 统一的返回按钮样式 */
.back-button {
  position: absolute;
  top: 15px;
  left: 15px;
  z-index: 10;
  width: 36px;
  height: 36px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  backdrop-filter: blur(5px);
  transition: all 0.2s;
}

.back-button:active {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 0.3);
}

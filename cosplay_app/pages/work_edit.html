<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>作品编辑 - Cosplay大赛</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../css/main.css">
    <link rel="stylesheet" href="../css/responsive.css">
    <style>
        .edit-form-header {
            background: linear-gradient(45deg, var(--primary), #6A1B9A);
            color: white;
            padding: 20px;
            border-radius: 0 0 20px 20px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            position: relative;
            margin-bottom: 20px;
        }

        .header-title {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 5px;
            text-align: center;
        }

        .header-subtitle {
            font-size: 14px;
            opacity: 0.9;
        }

        .back-button {
            position: absolute;
            top: 15px;
            left: 15px;
            z-index: 10;
            width: 36px;
            height: 36px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            backdrop-filter: blur(5px);
        }

        .form-container {
            padding: 0 20px 20px;
        }

        .form-section {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .section-title {
            font-size: 17px;
            font-weight: 600;
            margin-bottom: 15px;
            color: var(--dark);
            display: flex;
            align-items: center;
        }

        .section-title i {
            margin-right: 10px;
            color: var(--primary);
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group:last-child {
            margin-bottom: 0;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-size: 14px;
            color: var(--gray-700);
            font-weight: 500;
        }

        .form-control {
            width: 100%;
            max-width: 100%;
            box-sizing: border-box;
        }

        .image-upload-container {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin-top: 10px;
            width: 100%;
            box-sizing: border-box;
        }

        .image-upload-box {
            width: calc(33.33% - 7px);
            aspect-ratio: 1 / 1;
            background: var(--gray-100);
            border: 1px dashed var(--gray-400);
            border-radius: 10px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: var(--gray-500);
            cursor: pointer;
            overflow: hidden;
            position: relative;
            box-sizing: border-box;
        }

        .image-upload-box i {
            font-size: 20px;
            margin-bottom: 5px;
        }

        .image-upload-label {
            font-size: 12px;
        }

        .upload-preview {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .image-upload-input {
            display: none;
        }

        .image-count {
            margin-top: 8px;
            font-size: 12px;
            color: var(--gray-500);
            text-align: right;
        }

        .radio-options {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            width: 100%;
            box-sizing: border-box;
        }

        .radio-option {
            flex: 1;
            min-width: 100px;
            background: var(--gray-100);
            border: 1px solid var(--gray-300);
            border-radius: 12px;
            padding: 12px;
            text-align: center;
            font-size: 14px;
            color: var(--gray-700);
            cursor: pointer;
            transition: all 0.2s;
            box-sizing: border-box;
        }

        .radio-option.selected {
            background: rgba(138, 43, 226, 0.1);
            border-color: var(--primary);
            color: var(--primary);
            font-weight: 500;
        }

        .form-note {
            font-size: 13px;
            color: var(--gray-600);
            margin-top: 10px;
            border-left: 3px solid var(--warning);
            padding-left: 10px;
            background: rgba(221, 107, 32, 0.05);
            padding: 8px;
            border-radius: 5px;
        }

        .action-buttons {
            display: flex;
            gap: 15px;
            margin-top: 20px;
        }

        .action-btn {
            flex: 1;
            padding: 15px;
            border-radius: 12px;
            text-align: center;
            font-weight: bold;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .action-btn.cancel {
            background: white;
            color: var(--gray-600);
            border: 1px solid var(--gray-300);
        }

        .action-btn.submit {
            background: linear-gradient(45deg, var(--primary), #6A1B9A);
            color: white;
            box-shadow: 0 4px 15px rgba(138, 43, 226, 0.3);
        }

        .action-btn:hover {
            transform: translateY(-2px);
        }

        .action-btn.submit:hover {
            box-shadow: 0 6px 20px rgba(138, 43, 226, 0.4);
        }

        .reject-reason {
            background: rgba(229, 62, 62, 0.1);
            border-left: 3px solid var(--danger);
            padding: 12px;
            border-radius: 5px;
            margin-bottom: 20px;
        }

        .reason-title {
            font-weight: 500;
            color: var(--danger);
            margin-bottom: 5px;
            font-size: 14px;
        }

        .reason-text {
            font-size: 13px;
            color: var(--gray-700);
        }

        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.8);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            z-index: 100;
            backdrop-filter: blur(5px);
            display: none;
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            border: 3px solid var(--gray-300);
            border-top-color: var(--primary);
            animation: spinner 1s linear infinite;
            margin-bottom: 15px;
        }

        .loading-text {
            font-size: 16px;
            font-weight: 500;
            color: var(--primary);
        }

        @keyframes spinner {
            to {
                transform: rotate(360deg);
            }
        }
    </style>
</head>

<body>
    <div class="status-bar">
        <div class="status-bar-notch"></div>
        <div class="status-bar-left">
            <span>中国移动</span>
            <i class="fas fa-signal"></i>
        </div>
        <div class="status-bar-center status-bar-time">14:30</div>
        <div class="status-bar-right">
            <i class="fas fa-wifi"></i>
            <i class="fas fa-battery-three-quarters"></i>
        </div>
    </div>

    <div class="app-content">
        <div class="edit-form-header">
            <div class="back-button" onclick="window.location.href='audit_status.html'">
                <i class="fas fa-chevron-left"></i>
            </div>
            <div class="header-title">修改作品</div>
            <div class="header-subtitle" id="header-subtitle">完善作品信息</div>
        </div>

        <div class="form-container">
            <div class="reject-reason" id="reject-reason" style="display: none;">
                <div class="reason-title">审核未通过原因：</div>
                <div class="reason-text" id="reason-text">作品图片质量不清晰，请重新上传高清照片；角色描述信息不完整，请补充角色的详细介绍和创作理念。</div>
            </div>

            <div class="form-section">
                <div class="section-title">
                    <i class="fas fa-info-circle"></i>
                    作品信息
                </div>
                <div class="form-group">
                    <label class="form-label">作品名称</label>
                    <input type="text" class="form-control" value="星之守护者" placeholder="请输入作品名称">
                </div>
                <div class="form-group">
                    <label class="form-label">作品来源</label>
                    <input type="text" class="form-control" value="英雄联盟" placeholder="请输入作品来源">
                </div>
                <div class="form-group">
                    <label class="form-label">角色名称</label>
                    <input type="text" class="form-control" value="拉克丝" placeholder="请输入角色名称">
                </div>
                <div class="form-group">
                    <label class="form-label">是否原创</label>
                    <div class="radio-options">
                        <div class="radio-option" onclick="selectOption(this)">原创</div>
                        <div class="radio-option selected" onclick="selectOption(this)">非原创</div>
                    </div>
                </div>
            </div>

            <div class="form-section">
                <div class="section-title">
                    <i class="fas fa-images"></i>
                    作品照片
                </div>
                <div class="form-note">最多上传3张作品照片，建议分别展示不同角度，每张照片不超过5MB</div>
                <div class="image-upload-container">
                    <div class="image-upload-box">
                        <img src="https://images.unsplash.com/photo-1602410101088-3393e1f9aa0a?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80"
                            class="upload-preview" id="preview1">
                        <i class="fas fa-plus" id="icon1"></i>
                        <div class="image-upload-label" id="label1">上传照片</div>
                        <input type="file" class="image-upload-input" id="upload1" accept="image/*"
                            onchange="previewImage(this, 'preview1', 'icon1', 'label1')">
                    </div>
                    <div class="image-upload-box">
                        <img src="https://images.unsplash.com/photo-1577655197620-704858b270ac?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80"
                            class="upload-preview" id="preview2">
                        <i class="fas fa-plus" id="icon2"></i>
                        <div class="image-upload-label" id="label2">上传照片</div>
                        <input type="file" class="image-upload-input" id="upload2" accept="image/*"
                            onchange="previewImage(this, 'preview2', 'icon2', 'label2')">
                    </div>
                    <div class="image-upload-box">
                        <img src="https://images.unsplash.com/photo-1608889825271-9a2a6f3c98cb?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80"
                            class="upload-preview" id="preview3">
                        <i class="fas fa-plus" id="icon3"></i>
                        <div class="image-upload-label" id="label3">上传照片</div>
                        <input type="file" class="image-upload-input" id="upload3" accept="image/*"
                            onchange="previewImage(this, 'preview3', 'icon3', 'label3')">
                    </div>
                </div>
                <div class="image-count">已上传 3/3 张</div>
            </div>

            <div class="form-section">
                <div class="section-title">
                    <i class="fas fa-pen-fancy"></i>
                    创作说明
                </div>
                <div class="form-group">
                    <label class="form-label">作品介绍</label>
                    <textarea class="form-control" rows="4"
                        placeholder="请详细描述您的作品创意、制作过程和特色">星之守护者拉克丝是英雄联盟中备受喜爱的角色，这套服装是官方星之守护者系列皮肤。本次cosplay重现了拉克丝的星光魔杖和华丽服装，尤其注重了光效的处理。</textarea>
                </div>

            </div>

            <div class="action-buttons">
                <div class="action-btn cancel" onclick="window.location.href='audit_status.html'">取消</div>
                <div class="action-btn submit" onclick="submitForm()">提交审核</div>
            </div>
        </div>
    </div>

    <div class="loading-overlay" id="loading-overlay">
        <div class="loading-spinner"></div>
        <div class="loading-text">正在提交...</div>
    </div>

    <div class="bottom-nav">
        <div class="nav-item" data-target="home">
            <i class="fas fa-home"></i>
            <span>首页</span>
        </div>
        <div class="nav-item active" data-target="profile">
            <i class="fas fa-user"></i>
            <span>我的</span>
        </div>
    </div>

    <script src="../js/main.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            updateStatusBarTime();

            // 初始化上传预览
            const imageBoxes = document.querySelectorAll('.image-upload-box');

            imageBoxes.forEach(box => {
                const preview = box.querySelector('.upload-preview');
                const icon = box.querySelector('i');
                const label = box.querySelector('.image-upload-label');

                if (preview.src && preview.src !== '') {
                    preview.style.display = 'block';
                    icon.style.display = 'none';
                    label.style.display = 'none';
                }

                box.addEventListener('click', function () {
                    this.querySelector('.image-upload-input').click();
                });
            });

            // 检查URL参数，判断是否是审核不通过的情况
            const urlParams = new URLSearchParams(window.location.search);
            const rejected = urlParams.get('rejected');
            const reason = urlParams.get('reason');

            if (rejected === 'true') {
                document.getElementById('header-subtitle').textContent = '审核未通过，请按要求修改后重新提交';
                document.getElementById('reject-reason').style.display = 'block';

                if (reason) {
                    document.getElementById('reason-text').textContent = decodeURIComponent(reason);
                }
            }
        });

        function selectOption(el) {
            const options = el.parentElement.querySelectorAll('.radio-option');
            options.forEach(opt => opt.classList.remove('selected'));
            el.classList.add('selected');
        }

        function previewImage(input, previewId, iconId, labelId) {
            const preview = document.getElementById(previewId);
            const icon = document.getElementById(iconId);
            const label = document.getElementById(labelId);

            if (input.files && input.files[0]) {
                const reader = new FileReader();

                reader.onload = function (e) {
                    preview.src = e.target.result;
                    preview.style.display = 'block';
                    icon.style.display = 'none';
                    label.style.display = 'none';

                    // 更新计数
                    updateImageCount();
                };

                reader.readAsDataURL(input.files[0]);
            }
        }

        function updateImageCount() {
            const uploadedImages = document.querySelectorAll('.upload-preview[style*="display: block"]').length;
            document.querySelector('.image-count').textContent = `已上传 ${uploadedImages}/3 张`;
        }

        function submitForm() {
            const loadingOverlay = document.getElementById('loading-overlay');
            loadingOverlay.style.display = 'flex';

            // 模拟提交延迟
            setTimeout(() => {
                loadingOverlay.style.display = 'none';
                // 提交成功后跳转回审核状态页
                window.location.href = 'audit_status.html';
            }, 1500);
        }
    </script>
</body>

</html>
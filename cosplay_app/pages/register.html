<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>注册 - Cosplay大赛</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../css/main.css">
    <link rel="stylesheet" href="../css/responsive.css">
    <style>
        body {
            background: linear-gradient(145deg, #8A2BE2, #6A1B9A);
        }

        .register-container {
            height: 100vh;
            display: flex;
            flex-direction: column;
            margin-top: 40px;
        }

        .register-header {
            text-align: center;
            color: white;
            padding: 40px 20px 20px;
            position: relative;
        }

        .register-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .register-subtitle {
            font-size: 15px;
            opacity: 0.8;
        }

        .register-form-container {
            background: white;
            border-radius: 30px 30px 0 0;
            padding: 30px 20px;
            flex: 1;
            overflow-y: auto;
        }

        .register-form {
            max-width: 450px;
            margin: 0 auto;
            width: 100%;
            box-sizing: border-box;
        }

        .form-group {
            margin-bottom: 20px;
            width: 100%;
            box-sizing: border-box;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-size: 14px;
            color: var(--gray-700);
            font-weight: 500;
        }

        .input-with-icon {
            position: relative;
            width: 100%;
            box-sizing: border-box;
        }

        .input-with-icon .form-control {
            padding-left: 45px;
            width: 100%;
            box-sizing: border-box;
        }

        .input-with-icon i {
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--gray-500);
            font-size: 18px;
        }

        .form-control.error {
            border-color: var(--danger);
        }

        .form-error {
            color: var(--danger);
            font-size: 12px;
            margin-top: 5px;
            display: none;
        }

        .form-control.error+.form-error {
            display: block;
        }

        .verification-code {
            display: flex;
            gap: 10px;
            width: 100%;
            box-sizing: border-box;
        }

        .verification-code .form-control {
            flex: 1;
            min-width: 0;
            box-sizing: border-box;
        }

        .get-code-btn {
            flex: 0 0 auto;
            min-width: 85px;
            white-space: nowrap;
            padding: 0 15px;
            background: white;
            border: 1px solid var(--primary);
            color: var(--primary);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: 500;
            box-sizing: border-box;
        }

        .register-btn {
            background: linear-gradient(45deg, var(--primary), #6A1B9A);
            color: white;
            padding: 15px;
            border-radius: 12px;
            text-align: center;
            font-size: 16px;
            font-weight: bold;
            margin-top: 20px;
            box-shadow: 0 4px 15px rgba(138, 43, 226, 0.3);
            cursor: pointer;
            transition: all 0.2s;
            width: 100%;
            box-sizing: border-box;
        }

        .register-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(138, 43, 226, 0.4);
        }

        .terms-agreement {
            margin-top: 15px;
            font-size: 13px;
            color: var(--gray-600);
            display: flex;
            align-items: flex-start;
        }

        .terms-agreement input {
            margin-right: 10px;
            margin-top: 2px;
            flex-shrink: 0;
        }

        .terms-agreement label {
            flex: 1;
            line-height: 1.4;
        }

        .terms-link {
            color: var(--primary);
        }

        .back-to-login {
            text-align: center;
            margin-top: 20px;
            font-size: 14px;
            color: var(--gray-600);
        }

        .back-to-login a {
            color: var(--primary);
            text-decoration: none;
            font-weight: 500;
        }

        .back-button {
            position: absolute;
            top: 15px;
            left: 15px;
            z-index: 10;
            width: 36px;
            height: 36px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            backdrop-filter: blur(5px);
        }

        .register-steps {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
        }

        .register-step {
            flex: 1;
            text-align: center;
            position: relative;
        }

        .register-step::after {
            content: '';
            position: absolute;
            top: 15px;
            right: -50%;
            width: 100%;
            height: 2px;
            background: var(--gray-300);
            z-index: 1;
        }

        .register-step:last-child::after {
            display: none;
        }

        .step-number {
            width: 30px;
            height: 30px;
            background: var(--gray-300);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 10px;
            font-size: 14px;
            font-weight: 500;
            color: white;
            position: relative;
            z-index: 2;
        }

        .step-label {
            font-size: 13px;
            color: var(--gray-500);
        }

        .register-step.active .step-number {
            background: var(--primary);
        }

        .register-step.active .step-label {
            color: var(--primary);
            font-weight: 500;
        }

        .register-step.completed .step-number {
            background: var(--success);
        }

        .register-step.completed .step-number::after {
            content: '✓';
        }

        .register-step.completed::after {
            background: var(--success);
        }

        .avatar-upload {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-bottom: 20px;
        }

        .avatar-preview {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            background: var(--gray-200);
            margin-bottom: 15px;
            overflow: hidden;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
        }

        .avatar-preview img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            display: none;
        }

        .avatar-preview .upload-placeholder {
            display: flex;
            flex-direction: column;
            align-items: center;
            color: var(--gray-500);
        }

        .avatar-preview .upload-placeholder i {
            font-size: 24px;
            margin-bottom: 5px;
        }

        .avatar-preview .upload-placeholder span {
            font-size: 12px;
        }

        .terms-agreement {
            margin-top: 15px;
            font-size: 13px;
            color: var(--gray-600);
            display: flex;
            align-items: flex-start;
        }

        .terms-agreement input {
            margin-right: 10px;
            margin-top: 2px;
            flex-shrink: 0;
        }

        .terms-agreement label {
            flex: 1;
            line-height: 1.4;
        }

        .form-row {
            display: flex;
            gap: 15px;
            width: 100%;
            flex-wrap: wrap;
            box-sizing: border-box;
        }

        .form-row .form-group {
            flex: 1;
            min-width: 120px;
            box-sizing: border-box;
        }

        #id-upload {
            display: none;
        }
    </style>
</head>

<body>
    <div class="status-bar">
        <div class="status-bar-notch"></div>
        <div class="status-bar-left">
            <span>中国移动</span>
            <i class="fas fa-signal"></i>
        </div>
        <div class="status-bar-center status-bar-time">14:30</div>
        <div class="status-bar-right">
            <i class="fas fa-wifi"></i>
            <i class="fas fa-battery-three-quarters"></i>
        </div>
    </div>

    <div class="register-container">
        <div class="register-header">
            <div class="back-button" onclick="window.location.href='login.html'">
                <i class="fas fa-chevron-left"></i>
            </div>
            <div class="register-title">创建账号</div>
            <div class="register-subtitle">完成注册后即可参与Cosplay大赛</div>
        </div>

        <div class="register-form-container">
            <div class="register-form">
                <div class="register-steps">
                    <div class="register-step active">
                        <div class="step-number">1</div>
                        <div class="step-label">基本信息</div>
                    </div>
                    <div class="register-step">
                        <div class="step-number">2</div>
                        <div class="step-label">身份验证</div>
                    </div>
                    <div class="register-step">
                        <div class="step-number">3</div>
                        <div class="step-label">完成注册</div>
                    </div>
                </div>

                <div id="step-1" class="register-step-content">
                    <div class="avatar-upload">
                        <label for="avatar-upload" class="avatar-preview">
                            <img id="avatar-preview-img" src="" alt="头像预览">
                            <div class="upload-placeholder">
                                <i class="fas fa-camera"></i>
                                <span>设置头像</span>
                            </div>
                        </label>
                        <input type="file" id="avatar-upload" accept="image/*" style="display: none"
                            onchange="previewAvatar(this)">
                    </div>

                    <div class="form-group">
                        <label class="form-label">昵称</label>
                        <div class="input-with-icon">
                            <input type="text" class="form-control" placeholder="请输入昵称" required>
                            <i class="fas fa-user-tag"></i>
                        </div>
                        <div class="form-error">请输入昵称</div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">手机号码</label>
                        <div class="input-with-icon">
                            <input type="tel" class="form-control" placeholder="请输入手机号码" required>
                            <i class="fas fa-mobile-alt"></i>
                        </div>
                        <div class="form-error">请输入正确的手机号码</div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">验证码</label>
                        <div class="verification-code">
                            <div class="input-with-icon">
                                <input type="text" class="form-control" placeholder="请输入验证码" required>
                                <i class="fas fa-shield-alt"></i>
                            </div>
                            <button type="button" class="get-code-btn">获取验证码</button>
                        </div>
                        <div class="form-error">验证码不正确</div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">设置密码</label>
                        <div class="input-with-icon">
                            <input type="password" class="form-control" placeholder="请设置登录密码，不少于6位" required>
                            <i class="fas fa-lock"></i>
                        </div>
                        <div class="form-error">密码长度不能少于6位</div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">确认密码</label>
                        <div class="input-with-icon">
                            <input type="password" class="form-control" placeholder="请再次输入密码" required>
                            <i class="fas fa-lock"></i>
                        </div>
                        <div class="form-error">两次输入的密码不一致</div>
                    </div>

                    <div class="terms-agreement">
                        <input type="checkbox" id="terms-check" required>
                        <label for="terms-check">我已阅读并同意 <a href="#" class="terms-link">《用户服务协议》</a>和<a href="#"
                                class="terms-link">《隐私政策》</a></label>
                    </div>

                    <div class="register-btn" onclick="goToStep(2)">
                        下一步
                    </div>
                </div>

                <div id="step-2" class="register-step-content" style="display: none;">
                    <div class="form-group">
                        <label class="form-label">真实姓名</label>
                        <div class="input-with-icon">
                            <input type="text" class="form-control" placeholder="请输入真实姓名" required>
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="form-error">请输入真实姓名</div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">身份证号码</label>
                        <div class="input-with-icon">
                            <input type="text" class="form-control" placeholder="请输入身份证号码" required>
                            <i class="fas fa-id-card"></i>
                        </div>
                        <div class="form-error">请输入正确的身份证号码</div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">性别</label>
                            <select class="form-control">
                                <option value="">请选择</option>
                                <option value="male">男</option>
                                <option value="female">女</option>
                                <option value="other">其他</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label class="form-label">年龄</label>
                            <input type="number" class="form-control" placeholder="请输入年龄" min="16" max="120">
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">身份证照片</label>
                        <div class="input-with-icon">
                            <input type="text" class="form-control" placeholder="请上传身份证照片" readonly required>
                            <i class="fas fa-image"></i>
                        </div>
                        <label for="id-upload" class="avatar-upload-btn"
                            style="margin-top: 10px; width: 100%; justify-content: center;">
                            <i class="fas fa-upload"></i>
                            <span>上传身份证照片</span>
                        </label>
                        <input type="file" id="id-upload" accept="image/*">
                        <div class="form-error">请上传身份证照片</div>
                    </div>

                    <div class="register-btn" onclick="goToStep(3)">
                        下一步
                    </div>

                    <div class="back-to-login" onclick="goToStep(1)">
                        <a href="#">返回上一步</a>
                    </div>
                </div>

                <div id="step-3" class="register-step-content" style="display: none;">
                    <div style="text-align: center; margin: 30px 0;">
                        <i class="fas fa-check-circle" style="font-size: 60px; color: var(--success);"></i>
                        <h2 style="margin-top: 20px; color: var(--dark);">信息提交成功</h2>
                        <p style="margin-top: 10px; color: var(--gray-600);">信息已提交，请进行人脸识别以完成注册</p>
                    </div>

                    <div class="register-btn" onclick="window.location.href='face_recognition.html'">
                        开始人脸识别
                    </div>

                    <div class="back-to-login" onclick="goToStep(2)">
                        <a href="#">返回上一步</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="../js/main.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // 验证码按钮
            const codeBtn = document.querySelector('.get-code-btn');

            codeBtn.addEventListener('click', function () {
                // 模拟验证码发送
                this.disabled = true;
                let countdown = 60;
                this.textContent = `${countdown}秒后重发`;

                const timer = setInterval(() => {
                    countdown--;
                    this.textContent = `${countdown}秒后重发`;

                    if (countdown <= 0) {
                        clearInterval(timer);
                        this.disabled = false;
                        this.textContent = '获取验证码';
                    }
                }, 1000);
            });
        });

        // 预览头像
        function previewAvatar(input) {
            if (input.files && input.files[0]) {
                const reader = new FileReader();
                const previewImg = document.getElementById('avatar-preview-img');
                const placeholder = document.querySelector('.upload-placeholder');

                reader.onload = function (e) {
                    previewImg.src = e.target.result;
                    previewImg.style.display = 'block';
                    placeholder.style.display = 'none';
                };

                reader.readAsDataURL(input.files[0]);
            }
        }

        // 步骤切换
        function goToStep(step) {
            // 隐藏所有步骤内容
            document.querySelectorAll('.register-step-content').forEach(content => {
                content.style.display = 'none';
            });

            // 显示当前步骤内容
            document.getElementById(`step-${step}`).style.display = 'block';

            // 更新步骤指示器
            document.querySelectorAll('.register-step').forEach((stepEl, index) => {
                if (index + 1 < step) {
                    stepEl.classList.remove('active');
                    stepEl.classList.add('completed');
                } else if (index + 1 === step) {
                    stepEl.classList.add('active');
                    stepEl.classList.remove('completed');
                } else {
                    stepEl.classList.remove('active', 'completed');
                }
            });

            // 滚动到顶部
            window.scrollTo(0, 0);
        }
    </script>
</body>

</html>
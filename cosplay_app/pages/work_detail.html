<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>作品详情 - Cosplay大赛</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../css/main.css">
    <link rel="stylesheet" href="../css/responsive.css">
    <style>
        .work-detail {
            padding-bottom: 100px;
        }

        .back-button {
            position: absolute;
            top: 15px;
            left: 15px;
            z-index: 10;
            width: 36px;
            height: 36px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }

        .share-button {
            position: absolute;
            top: 15px;
            right: 15px;
            z-index: 10;
            width: 36px;
            height: 36px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }

        .image-gallery {
            position: relative;
        }

        .gallery-main {
            width: 100%;
            height: 70vh;
            position: relative;
        }

        .gallery-main img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .gallery-overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            padding: 100px 20px 20px;
            background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
            color: white;
        }

        .gallery-title {
            font-size: 22px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .gallery-subtitle {
            font-size: 16px;
            opacity: 0.9;
            margin-bottom: 10px;
        }

        .gallery-badges {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }

        .gallery-badge {
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 12px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
        }

        .gallery-badge.gold {
            background: linear-gradient(45deg, #FFD700, #FFA500);
        }

        .gallery-thumbnails {
            display: flex;
            padding: 15px;
            gap: 10px;
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
            scrollbar-width: none;
        }

        .gallery-thumbnails::-webkit-scrollbar {
            display: none;
        }

        .gallery-thumb {
            width: 60px;
            height: 60px;
            border-radius: 10px;
            overflow: hidden;
            flex-shrink: 0;
            border: 2px solid transparent;
            opacity: 0.7;
            transition: all 0.2s;
        }

        .gallery-thumb.active {
            border-color: var(--primary);
            opacity: 1;
        }

        .gallery-thumb img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .detail-section {
            padding: 20px;
            border-bottom: 8px solid var(--gray-100);
        }

        .section-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
            color: var(--gray-800);
            display: flex;
            align-items: center;
        }

        .section-title i {
            margin-right: 10px;
            color: var(--primary);
        }

        .author-card {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }

        .author-avatar {
            width: 50px;
            height: 50px;
            border-radius: 25px;
            margin-right: 15px;
        }

        .author-info {
            flex: 1;
        }

        .author-name {
            font-size: 16px;
            font-weight: 500;
            color: var(--gray-800);
            margin-bottom: 4px;
        }

        .author-bio {
            font-size: 13px;
            color: var(--gray-600);
        }

        .follow-btn {
            padding: 6px 12px;
            border-radius: 20px;
            background: var(--primary);
            color: white;
            font-size: 13px;
            font-weight: 500;
        }

        .detail-info {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            margin-bottom: 15px;
        }

        .info-item {
            background: var(--gray-100);
            padding: 10px 15px;
            border-radius: 10px;
            font-size: 13px;
            color: var(--gray-700);
            flex: 1;
            min-width: 120px;
            display: flex;
            flex-direction: column;
        }

        .info-label {
            color: var(--gray-500);
            margin-bottom: 5px;
            font-size: 12px;
        }

        .info-value {
            font-weight: 500;
        }

        .work-description {
            font-size: 15px;
            line-height: 1.6;
            color: var(--gray-700);
            margin-bottom: 15px;
        }

        .stats-container {
            display: flex;
            justify-content: space-between;
            padding: 15px 0;
            border-top: 1px solid var(--gray-200);
            margin-top: 15px;
        }

        .stat-item {
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .stat-value {
            font-weight: 600;
            color: var(--gray-800);
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 12px;
            color: var(--gray-500);
        }

        .comment-item {
            border-bottom: 1px solid var(--gray-200);
            padding: 15px 0;
        }

        .comment-item:last-child {
            border-bottom: none;
        }

        .comment-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
        }

        .comment-author {
            display: flex;
            align-items: center;
        }

        .comment-avatar {
            width: 36px;
            height: 36px;
            border-radius: 18px;
            margin-right: 10px;
        }

        .comment-name {
            font-weight: 500;
            color: var(--gray-800);
            margin-bottom: 2px;
        }

        .comment-date {
            font-size: 12px;
            color: var(--gray-500);
        }

        .comment-like {
            color: var(--gray-500);
            font-size: 13px;
            display: flex;
            align-items: center;
        }

        .comment-like i {
            margin-right: 5px;
        }

        .comment-like.active {
            color: var(--danger);
        }

        .comment-text {
            font-size: 14px;
            line-height: 1.5;
            color: var(--gray-700);
            margin-bottom: 10px;
        }

        .comment-images {
            display: flex;
            gap: 10px;
            margin-bottom: 10px;
        }

        .comment-image {
            width: 80px;
            height: 80px;
            border-radius: 8px;
            overflow: hidden;
        }

        .comment-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .similar-works {
            padding: 20px;
        }

        .similar-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
            color: var(--gray-800);
        }

        .similar-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
        }

        .similar-card {
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);
        }

        .similar-img {
            width: 100%;
            height: 160px;
            object-fit: cover;
        }

        .similar-info {
            padding: 10px;
        }

        .similar-name {
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 5px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .similar-author {
            font-size: 12px;
            color: var(--gray-600);
        }

        .action-bar {
            position: fixed;
            bottom: 90px;
            left: 0;
            right: 0;
            background: white;
            padding: 10px 20px;
            display: flex;
            align-items: center;
            border-top: 1px solid var(--gray-200);
            z-index: 90;
        }

        .comment-input {
            flex: 1;
            height: 40px;
            border: 1px solid var(--gray-300);
            border-radius: 20px;
            padding: 0 15px;
            display: flex;
            align-items: center;
            color: var(--gray-500);
            margin-right: 15px;
        }

        .action-buttons {
            display: flex;
            gap: 20px;
        }

        .action-btn {
            display: flex;
            flex-direction: column;
            align-items: center;
            font-size: 10px;
            color: var(--gray-600);
        }

        .action-btn i {
            font-size: 20px;
            margin-bottom: 4px;
        }

        .action-btn.active {
            color: var(--primary);
        }

        .action-btn.liked {
            color: var(--danger);
        }
    </style>
</head>

<body>
    <div class="status-bar">
        <div class="status-bar-notch"></div>
        <div class="status-bar-left">
            <span>中国移动</span>
            <i class="fas fa-signal"></i>
        </div>
        <div class="status-bar-center status-bar-time">14:30</div>
        <div class="status-bar-right">
            <i class="fas fa-wifi"></i>
            <i class="fas fa-battery-three-quarters"></i>
        </div>
    </div>

    <div class="app-content">
        <div class="work-detail">
            <div class="image-gallery">
                <div class="back-button" onclick="window.location.href='work_list.html'">
                    <i class="fas fa-chevron-left"></i>
                </div>
                <div class="share-button">
                    <i class="fas fa-share-alt"></i>
                </div>
                <div class="gallery-main">
                    <img src="https://images.unsplash.com/photo-1530071100468-90fe162e2a20?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80"
                        alt="作品主图" id="mainImage">
                    <div class="gallery-overlay">
                        <div class="gallery-badges">
                            <div class="gallery-badge gold">金奖</div>
                            <div class="gallery-badge">2023赛季</div>
                        </div>
                        <div class="gallery-title">星之守护者 - 拉克丝</div>
                        <div class="gallery-subtitle">英雄联盟</div>
                    </div>
                </div>
                <div class="gallery-thumbnails">
                    <div class="gallery-thumb active" onclick="changeImage(0)">
                        <img src="https://images.unsplash.com/photo-1530071100468-90fe162e2a20?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80"
                            alt="缩略图1">
                    </div>
                    <div class="gallery-thumb" onclick="changeImage(1)">
                        <img src="https://images.unsplash.com/photo-1563804447127-f4ef4ff5f1d1?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80"
                            alt="缩略图2">
                    </div>
                    <div class="gallery-thumb" onclick="changeImage(2)">
                        <img src="https://images.unsplash.com/photo-1566454825481-4a8a4eda5094?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80"
                            alt="缩略图3">
                    </div>
                    <div class="gallery-thumb" onclick="changeImage(3)">
                        <img src="https://images.unsplash.com/photo-1591417033176-0ca2633c1bcf?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80"
                            alt="缩略图4">
                    </div>
                    <div class="gallery-thumb" onclick="changeImage(4)">
                        <img src="https://images.unsplash.com/photo-1569443693539-175ea9f007e8?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80"
                            alt="缩略图5">
                    </div>
                </div>
            </div>

            <div class="detail-section">
                <div class="section-title">
                    <i class="fas fa-user"></i>
                    作者信息
                </div>
                <div class="author-card">
                    <img src="https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-1.2.1&auto=format&fit=crop&w=200&q=80"
                        alt="作者头像" class="author-avatar">
                    <div class="author-info">
                        <div class="author-name">梦幻Cosplayer</div>
                        <div class="author-bio">专注于英雄联盟角色还原 | 2年Coser经验</div>
                    </div>
                    <div class="follow-btn">+ 关注</div>
                </div>
            </div>

            <div class="detail-section">
                <div class="section-title">
                    <i class="fas fa-info-circle"></i>
                    作品信息
                </div>
                <div class="detail-info">
                    <div class="info-item">
                        <div class="info-label">作品名称</div>
                        <div class="info-value">星之守护者 - 拉克丝</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">角色来源</div>
                        <div class="info-value">英雄联盟</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">上传时间</div>
                        <div class="info-value">2023-09-15</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">比赛成绩</div>
                        <div class="info-value">金奖</div>
                    </div>
                </div>
                <div class="work-description">
                    这是我参加2023星空Cosplay大赛的参赛作品，选择了英雄联盟中的星之守护者拉克丝。服装是自己手工制作的，历时两个月完成，细节部分尽可能还原了游戏原设，魔杖的机关部分可以发光。拍摄赛区选在了天文台，配合星空背景，希望大家喜欢！
                </div>
                <div class="stats-container">
                    <div class="stat-item">
                        <div class="stat-value">5.8k</div>
                        <div class="stat-label">浏览</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">1.2k</div>
                        <div class="stat-label">点赞</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">342</div>
                        <div class="stat-label">收藏</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">86</div>
                        <div class="stat-label">评论</div>
                    </div>
                </div>
            </div>

            <div class="detail-section">
                <div class="section-title">
                    <i class="fas fa-comment-dots"></i>
                    作品评论
                </div>
                <div class="comment-item">
                    <div class="comment-header">
                        <div class="comment-author">
                            <img src="https://images.unsplash.com/photo-1599566150163-29194dcaad36?ixlib=rb-1.2.1&auto=format&fit=crop&w=200&q=80"
                                alt="评论者头像" class="comment-avatar">
                            <div>
                                <div class="comment-name">光之魔导师</div>
                                <div class="comment-date">2023-09-17</div>
                            </div>
                        </div>
                        <div class="comment-like active">
                            <i class="fas fa-heart"></i>
                            <span>25</span>
                        </div>
                    </div>
                    <div class="comment-text">
                        太惊艳了！魔杖的细节处理非常棒，发光效果与星空背景相得益彰。我也是拉克丝玩家，这套cos真的还原度超高，特别是头饰的部分，很精致。
                    </div>
                </div>
                <div class="comment-item">
                    <div class="comment-header">
                        <div class="comment-author">
                            <img src="https://images.unsplash.com/photo-1607746882042-944635dfe10e?ixlib=rb-1.2.1&auto=format&fit=crop&w=200&q=80"
                                alt="评论者头像" class="comment-avatar">
                            <div>
                                <div class="comment-name">星光追逐者</div>
                                <div class="comment-date">2023-09-16</div>
                            </div>
                        </div>
                        <div class="comment-like">
                            <i class="far fa-heart"></i>
                            <span>18</span>
                        </div>
                    </div>
                    <div class="comment-text">
                        服装真的是自己做的吗？质感也太好了吧！可以分享一下制作过程吗？我也准备尝试做一套星之守护者的服装，想向你取取经。
                    </div>
                    <div class="comment-images">
                        <div class="comment-image">
                            <img src="https://images.unsplash.com/photo-1547153760-18fc86324498?ixlib=rb-1.2.1&auto=format&fit=crop&w=200&q=80"
                                alt="评论配图">
                        </div>
                    </div>
                </div>
                <div class="comment-item">
                    <div class="comment-header">
                        <div class="comment-author">
                            <img src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-1.2.1&auto=format&fit=crop&w=200&q=80"
                                alt="评论者头像" class="comment-avatar">
                            <div>
                                <div class="comment-name">摄影师阿文</div>
                                <div class="comment-date">2023-09-15</div>
                            </div>
                        </div>
                        <div class="comment-like">
                            <i class="far fa-heart"></i>
                            <span>12</span>
                        </div>
                    </div>
                    <div class="comment-text">
                        构图和光影处理很专业，完全把角色的神韵展现出来了。不知道是摄影师还是自己设计的场景，总之效果很棒！金奖实至名归。
                    </div>
                </div>
            </div>

            <div class="similar-works">
                <div class="similar-title">相似作品</div>
                <div class="similar-grid">
                    <div class="similar-card" onclick="window.location.href='work_detail.html'">
                        <img src="https://images.unsplash.com/photo-1563804447127-f4ef4ff5f1d1?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80"
                            alt="相似作品" class="similar-img">
                        <div class="similar-info">
                            <div class="similar-name">魔卡少女 - 小樱</div>
                            <div class="similar-author">樱花之舞</div>
                        </div>
                    </div>
                    <div class="similar-card" onclick="window.location.href='work_detail.html'">
                        <img src="https://images.unsplash.com/photo-1566454825481-4a8a4eda5094?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80"
                            alt="相似作品" class="similar-img">
                        <div class="similar-info">
                            <div class="similar-name">最终幻想VII - 蒂法</div>
                            <div class="similar-author">幻想勇者</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="action-bar">
            <div class="comment-input">
                <i class="far fa-comment-dots"></i>
                <span style="margin-left: 10px;">评论...</span>
            </div>
            <div class="action-buttons">
                <div class="action-btn" id="like-btn">
                    <i class="far fa-heart"></i>
                    <span>1.2k</span>
                </div>
                <div class="action-btn" id="favorite-btn">
                    <i class="far fa-star"></i>
                    <span>收藏</span>
                </div>
            </div>
        </div>
    </div>

    <div class="bottom-nav">
        <div class="nav-item" data-target="home">
            <i class="fas fa-home"></i>
            <span>首页</span>
        </div>
        <div class="nav-item" data-target="events">
            <i class="fas fa-calendar-alt"></i>
            <span>活动</span>
        </div>
        <div class="nav-item active" data-target="work_list">
            <i class="fas fa-images"></i>
            <span>作品</span>
        </div>
        <div class="nav-item" data-target="profile">
            <i class="fas fa-user"></i>
            <span>我的</span>
        </div>
    </div>

    <script src="../js/main.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // 更新状态栏时间
            updateStatusBarTime();

            // 点赞按钮
            const likeBtn = document.getElementById('like-btn');
            likeBtn.addEventListener('click', function () {
                const icon = this.querySelector('i');
                if (icon.classList.contains('far')) {
                    icon.classList.remove('far');
                    icon.classList.add('fas');
                    this.classList.add('liked');
                    this.querySelector('span').textContent = '1.2k';
                } else {
                    icon.classList.remove('fas');
                    icon.classList.add('far');
                    this.classList.remove('liked');
                    this.querySelector('span').textContent = '1.2k';
                }
            });

            // 收藏按钮
            const favoriteBtn = document.getElementById('favorite-btn');
            favoriteBtn.addEventListener('click', function () {
                const icon = this.querySelector('i');
                if (icon.classList.contains('far')) {
                    icon.classList.remove('far');
                    icon.classList.add('fas');
                    this.classList.add('active');
                    this.querySelector('span').textContent = '已收藏';
                } else {
                    icon.classList.remove('fas');
                    icon.classList.add('far');
                    this.classList.remove('active');
                    this.querySelector('span').textContent = '收藏';
                }
            });

            // 评论点赞
            const commentLikes = document.querySelectorAll('.comment-like');
            commentLikes.forEach(like => {
                like.addEventListener('click', function () {
                    const icon = this.querySelector('i');
                    const count = this.querySelector('span');

                    if (icon.classList.contains('far')) {
                        icon.classList.remove('far');
                        icon.classList.add('fas');
                        this.classList.add('active');
                        count.textContent = (parseInt(count.textContent) + 1).toString();
                    } else {
                        icon.classList.remove('fas');
                        icon.classList.add('far');
                        this.classList.remove('active');
                        count.textContent = (parseInt(count.textContent) - 1).toString();
                    }
                });
            });

            // 评论输入框
            const commentInput = document.querySelector('.comment-input');
            commentInput.addEventListener('click', function () {
                alert('评论功能将在后续版本中开放');
            });

            // 分享按钮
            const shareButton = document.querySelector('.share-button');
            shareButton.addEventListener('click', function () {
                alert('分享功能将在后续版本中开放');
            });
        });

        // 图片切换功能
        const images = [
            "https://images.unsplash.com/photo-1530071100468-90fe162e2a20?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
            "https://images.unsplash.com/photo-1563804447127-f4ef4ff5f1d1?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
            "https://images.unsplash.com/photo-1566454825481-4a8a4eda5094?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
            "https://images.unsplash.com/photo-1591417033176-0ca2633c1bcf?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80",
            "https://images.unsplash.com/photo-1569443693539-175ea9f007e8?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80"
        ];

        function changeImage(index) {
            const mainImage = document.getElementById('mainImage');
            mainImage.src = images[index];

            // 更新缩略图状态
            const thumbnails = document.querySelectorAll('.gallery-thumb');
            thumbnails.forEach((thumb, i) => {
                if (i === index) {
                    thumb.classList.add('active');
                } else {
                    thumb.classList.remove('active');
                }
            });
        }
    </script>
</body>

</html>
<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人中心 - Cosplay大赛</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../css/main.css">
    <link rel="stylesheet" href="../css/responsive.css">
    <style>
        .profile-page {
            padding-bottom: 100px;
        }

        .profile-header {
            position: relative;
            padding-top: 20px;
            margin-bottom: 20px;
        }

        .header-background {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 130px;
            background: linear-gradient(45deg, var(--primary), #9932cc);
            border-radius: 0 0 30px 30px;
            z-index: 1;
        }

        .header-content {
            position: relative;
            z-index: 2;
            padding: 0 20px;
            padding-bottom: 15px;
        }

        .avatar-container {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            padding: 0 15px;
        }

        .avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            border: 3px solid white;
            object-fit: cover;
            background: #fff;
            margin-right: 15px;
        }

        .user-info {
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .username {
            color: #ffffff;
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .user-id {
            color: #cccccc;
            font-size: 14px;
        }

        .edit-profile {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: none;
            border-radius: 20px;
            padding: 6px 15px;
            font-size: 13px;
            font-weight: 500;
            cursor: pointer;
            backdrop-filter: blur(5px);
        }

        .user-bio {
            color: white;
            font-size: 14px;
            text-align: center;
            margin-bottom: 20px;
            line-height: 1.5;
        }

        .works-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-bottom: 20px;
        }

        .work-card {
            border-radius: 12px;
            overflow: hidden;
            background: white;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);
            position: relative;
        }

        .work-image {
            width: 100%;
            height: 160px;
            object-fit: cover;
        }

        .work-status {
            position: absolute;
            top: 10px;
            right: 10px;
            padding: 3px 8px;
            border-radius: 10px;
            font-size: 11px;
            font-weight: 500;
            background: rgba(0, 0, 0, 0.6);
            color: white;
            backdrop-filter: blur(4px);
        }

        .work-status.passed {
            background: rgba(40, 167, 69, 0.8);
        }

        .work-status.pending {
            background: rgba(255, 193, 7, 0.8);
        }

        .work-status.rejected {
            background: rgba(220, 53, 69, 0.8);
        }

        .work-info {
            padding: 10px;
        }

        .work-title {
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 5px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            color: var(--gray-800);
        }

        .work-stats {
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            color: var(--gray-500);
        }

        .section-title {
            font-size: 17px;
            font-weight: 600;
            color: var(--gray-800);
            margin: 15px 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .view-all {
            font-size: 13px;
            font-weight: normal;
            color: var(--primary);
        }

        .menu-section {
            padding: 15px 20px 5px;
        }

        .qr-code-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin: 20px;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);
        }

        .qr-code-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 15px;
            color: var(--gray-800);
        }

        .qr-code {
            width: 200px;
            height: 200px;
            margin-bottom: 15px;
            border: 1px solid var(--gray-200);
            padding: 10px;
            border-radius: 8px;
        }

        .qr-code img {
            width: 100%;
            height: 100%;
        }

        .qr-code-desc {
            font-size: 14px;
            color: var(--gray-600);
            text-align: center;
            max-width: 250px;
            line-height: 1.5;
        }

        .menu-list {
            padding: 0 20px;
            margin-bottom: 30px;
            margin-top: 5px;
        }

        .menu-item {
            display: flex;
            align-items: center;
            padding: 15px;
            background: white;
            border-radius: 12px;
            margin-bottom: 10px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
        }

        .menu-icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            color: white;
            font-size: 18px;
        }

        .menu-icon.works {
            background: linear-gradient(135deg, #9333EA, #6B21A8);
        }

        .menu-icon.qrcode {
            background: linear-gradient(135deg, #10B981, #059669);
        }

        .menu-icon.notification {
            background: linear-gradient(135deg, #F59E0B, #D97706);
        }

        .menu-icon.profile {
            background: linear-gradient(135deg, #3B82F6, #2563EB);
        }

        .menu-icon.help {
            background: linear-gradient(135deg, #EC4899, #DB2777);
        }

        .menu-icon.about {
            background: linear-gradient(135deg, #8B5CF6, #7C3AED);
        }

        .menu-icon.scan {
            background: linear-gradient(135deg, #EF4444, #B91C1C);
        }

        .menu-text {
            flex: 1;
        }

        .menu-title {
            font-size: 16px;
            font-weight: 500;
            color: var(--gray-800);
            margin-bottom: 3px;
        }

        .menu-desc {
            font-size: 13px;
            color: var(--gray-500);
        }

        .menu-arrow {
            color: var(--gray-400);
        }

        .logout-button {
            display: block;
            width: calc(100% - 40px);
            padding: 15px;
            text-align: center;
            background: white;
            border-radius: 12px;
            color: #dc3545;
            font-size: 16px;
            font-weight: 500;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            border: none;
            cursor: pointer;
            margin: 20px;
        }
    </style>
</head>

<body>
    <div class="status-bar">
        <div class="status-bar-notch"></div>
        <div class="status-bar-left">
            <span>中国移动</span>
            <i class="fas fa-signal"></i>
        </div>
        <div class="status-bar-center status-bar-time">14:30</div>
        <div class="status-bar-right">
            <i class="fas fa-wifi"></i>
            <i class="fas fa-battery-three-quarters"></i>
        </div>
    </div>

    <div class="app-content">
        <div class="profile-page">
            <div class="profile-header">
                <div class="header-background"></div>
                <div class="header-content">
                    <div class="avatar-container">
                        <img src="https://i.pravatar.cc/200?img=35" alt="用户头像" class="avatar">
                        <div class="user-info">
                            <h1 class="username">梦幻Cosplayer</h1>
                            <div class="user-id">ID: 2023765</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="menu-list">
                <div class="menu-item" onclick="window.location.href='audit_status.html'">
                    <div class="menu-icon works">
                        <i class="fas fa-clipboard-check"></i>
                    </div>
                    <div class="menu-text">
                        <div class="menu-title">我的报名</div>
                        <div class="menu-desc">查看和管理您的报名信息</div>
                    </div>
                    <div class="menu-arrow">
                        <i class="fas fa-chevron-right"></i>
                    </div>
                </div>

                <div class="menu-item" onclick="showQRCode()">
                    <div class="menu-icon qrcode">
                        <i class="fas fa-qrcode"></i>
                    </div>
                    <div class="menu-text">
                        <div class="menu-title">我的身份码</div>
                        <div class="menu-desc">显示签到和身份验证二维码</div>
                    </div>
                    <div class="menu-arrow">
                        <i class="fas fa-chevron-right"></i>
                    </div>
                </div>

                <div class="menu-item" onclick="window.location.href='check_in_scan.html'">
                    <div class="menu-icon scan">
                        <i class="fas fa-camera"></i>
                    </div>
                    <div class="menu-text">
                        <div class="menu-title">扫码签到</div>
                        <div class="menu-desc">工作人员专用 - 扫描选手二维码签到</div>
                    </div>
                    <div class="menu-arrow">
                        <i class="fas fa-chevron-right"></i>
                    </div>
                </div>

                <div class="menu-item" onclick="window.location.href='notification.html'">
                    <div class="menu-icon notification">
                        <i class="fas fa-bell"></i>
                    </div>
                    <div class="menu-text">
                        <div class="menu-title">消息通知</div>
                        <div class="menu-desc">查看系统消息和活动提醒</div>
                    </div>
                    <div class="menu-arrow">
                        <i class="fas fa-chevron-right"></i>
                    </div>
                </div>

                <div class="menu-item" onclick="window.location.href='user_profile.html'">
                    <div class="menu-icon profile">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="menu-text">
                        <div class="menu-title">个人资料</div>
                        <div class="menu-desc">管理您的账号和个人信息</div>
                    </div>
                    <div class="menu-arrow">
                        <i class="fas fa-chevron-right"></i>
                    </div>
                </div>

                <div class="menu-item" onclick="window.location.href='contact_service.html'">
                    <div class="menu-icon help">
                        <i class="fas fa-headset"></i>
                    </div>
                    <div class="menu-text">
                        <div class="menu-title">联系客服</div>
                        <div class="menu-desc">在线咨询和问题反馈</div>
                    </div>
                    <div class="menu-arrow">
                        <i class="fas fa-chevron-right"></i>
                    </div>
                </div>

                <div class="menu-item" onclick="window.location.href='about_us.html'">
                    <div class="menu-icon about">
                        <i class="fas fa-info-circle"></i>
                    </div>
                    <div class="menu-text">
                        <div class="menu-title">关于我们</div>
                        <div class="menu-desc">了解平台信息和联系方式</div>
                    </div>
                    <div class="menu-arrow">
                        <i class="fas fa-chevron-right"></i>
                    </div>
                </div>
            </div>

            <button class="logout-button">退出登录</button>

            <div class="qr-code-container" id="qrcode-container" style="display: none;">
                <div class="qr-code-title">我的身份码</div>
                <div class="qr-code">
                    <img src="https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=ID:2023765" alt="身份二维码">
                </div>
                <div class="qr-code-desc">请向工作人员出示此二维码完成签到或身份验证</div>
            </div>
        </div>
    </div>

    <div class="bottom-nav">
        <div class="nav-item" data-target="home">
            <i class="fas fa-home"></i>
            <span>首页</span>
        </div>
        <div class="nav-item active" data-target="profile">
            <i class="fas fa-user"></i>
            <span>我的</span>
        </div>
    </div>

    <script src="../js/main.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // 更新状态栏时间
            updateStatusBarTime();

            // 底部导航栏功能
            document.querySelectorAll('.nav-item').forEach(item => {
                item.addEventListener('click', function () {
                    const target = this.getAttribute('data-target');
                    if (target !== 'profile') {
                        window.location.href = target + '.html';
                    }
                });
            });

            // 退出登录按钮
            document.querySelector('.logout-button').addEventListener('click', function () {
                if (confirm('确定要退出登录吗？')) {
                    window.location.href = 'login.html';
                }
            });

            // 菜单项点击事件
            document.querySelectorAll('.menu-item').forEach(item => {
                item.addEventListener('click', function () {
                    // 如果有特定的点击处理函数，不执行默认提示
                    if (!this.hasAttribute('onclick')) {
                        alert('此功能将在后续版本中开放');
                    }
                });
            });
        });

        // 显示二维码
        function showQRCode() {
            const qrcodeContainer = document.getElementById('qrcode-container');
            if (qrcodeContainer.style.display === 'none') {
                qrcodeContainer.style.display = 'flex';

                // 滚动到二维码位置
                qrcodeContainer.scrollIntoView({ behavior: 'smooth' });
            } else {
                qrcodeContainer.style.display = 'none';
            }
        }
    </script>
</body>

</html>
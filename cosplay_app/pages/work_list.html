<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>作品列表 - Cosplay大赛</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../css/main.css">
    <link rel="stylesheet" href="../css/responsive.css">
    <style>
        .works-header {
            padding: 20px;
            background: var(--white);
            border-bottom: 1px solid var(--gray-200);
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .header-title {
            font-size: 22px;
            font-weight: 600;
            margin-bottom: 15px;
            color: var(--gray-800);
        }

        .search-bar {
            position: relative;
            margin-bottom: 15px;
            width: 100%;
            max-width: 100%;
            box-sizing: border-box;
        }

        .search-input {
            width: 100%;
            max-width: 100%;
            padding: 12px 45px 12px 15px;
            border-radius: 12px;
            border: 1px solid var(--gray-300);
            background: var(--gray-100);
            font-size: 14px;
            box-sizing: border-box;
        }

        .search-icon {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--gray-500);
        }

        .filter-tabs {
            display: flex;
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
            scrollbar-width: none;
            gap: 10px;
            padding-bottom: 5px;
        }

        .filter-tabs::-webkit-scrollbar {
            display: none;
        }

        .filter-tab {
            padding: 8px 16px;
            border-radius: 20px;
            background: var(--gray-100);
            color: var(--gray-600);
            font-size: 13px;
            white-space: nowrap;
            transition: all 0.2s;
        }

        .filter-tab.active {
            background: var(--primary);
            color: var(--white);
        }

        .works-container {
            padding: 15px;
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-bottom: 70px;
        }

        .work-card {
            border-radius: 12px;
            overflow: hidden;
            background: var(--white);
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);
            transition: transform 0.2s;
        }

        .work-card:active {
            transform: scale(0.98);
        }

        .work-image {
            position: relative;
            width: 100%;
            padding-top: 125%;
            /* 4:5 aspect ratio */
            overflow: hidden;
        }

        .work-image img {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .work-badge {
            position: absolute;
            top: 10px;
            right: 10px;
            padding: 4px 8px;
            border-radius: 20px;
            font-size: 11px;
            font-weight: 500;
            background: rgba(0, 0, 0, 0.6);
            color: var(--white);
            backdrop-filter: blur(4px);
        }

        .work-badge.gold {
            background: linear-gradient(45deg, #FFD700, #FFA500);
        }

        .work-badge.silver {
            background: linear-gradient(45deg, #C0C0C0, #A9A9A9);
        }

        .work-badge.bronze {
            background: linear-gradient(45deg, #CD7F32, #A0522D);
        }

        .work-info {
            padding: 12px;
        }

        .work-title {
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 5px;
            color: var(--gray-800);
            display: -webkit-box;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .work-author {
            font-size: 12px;
            color: var(--gray-600);
            margin-bottom: 8px;
            display: flex;
            align-items: center;
        }

        .work-author i {
            margin-right: 5px;
            font-size: 10px;
        }

        .work-stats {
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            color: var(--gray-500);
        }

        .stat {
            display: flex;
            align-items: center;
        }

        .stat i {
            margin-right: 4px;
        }

        .filter-button {
            position: fixed;
            bottom: 110px;
            right: 20px;
            width: 50px;
            height: 50px;
            border-radius: 25px;
            background: var(--primary);
            color: var(--white);
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 3px 10px rgba(138, 43, 226, 0.3);
            z-index: 90;
        }

        .filter-drawer {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: var(--white);
            border-radius: 20px 20px 0 0;
            padding: 20px;
            box-shadow: 0 -3px 15px rgba(0, 0, 0, 0.1);
            transform: translateY(100%);
            transition: transform 0.3s;
            z-index: 120;
        }

        .filter-drawer.open {
            transform: translateY(0);
        }

        .drawer-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .drawer-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--gray-800);
        }

        .close-button {
            width: 30px;
            height: 30px;
            border-radius: 15px;
            background: var(--gray-100);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--gray-600);
        }

        .filter-section {
            margin-bottom: 20px;
        }

        .filter-section-title {
            font-size: 15px;
            font-weight: 500;
            margin-bottom: 10px;
            color: var(--gray-700);
        }

        .filter-options {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }

        .filter-option {
            padding: 8px 14px;
            border-radius: 20px;
            background: var(--gray-100);
            color: var(--gray-600);
            font-size: 13px;
        }

        .filter-option.selected {
            background: var(--primary-light);
            color: var(--primary);
        }

        .filter-actions {
            display: flex;
            gap: 15px;
            margin-top: 20px;
        }

        .filter-action {
            flex: 1;
            padding: 12px;
            border-radius: 12px;
            text-align: center;
            font-weight: 500;
            font-size: 15px;
        }

        .filter-action.reset {
            background: var(--gray-100);
            color: var(--gray-600);
        }

        .filter-action.apply {
            background: var(--primary);
            color: var(--white);
        }

        .overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 110;
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.3s;
        }

        .overlay.show {
            opacity: 1;
            pointer-events: auto;
        }

        .load-more {
            padding: 12px;
            text-align: center;
            color: var(--primary);
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 90px;
        }
    </style>
</head>

<body>
    <div class="status-bar">
        <div class="status-bar-notch"></div>
        <div class="status-bar-left">
            <span>中国移动</span>
            <i class="fas fa-signal"></i>
        </div>
        <div class="status-bar-center status-bar-time">14:30</div>
        <div class="status-bar-right">
            <i class="fas fa-wifi"></i>
            <i class="fas fa-battery-three-quarters"></i>
        </div>
    </div>

    <div class="app-content">
        <div class="works-header">
            <h1 class="header-title">作品展示</h1>
            <div class="search-bar">
                <input type="text" class="search-input" placeholder="搜索作品、作者或角色">
                <i class="fas fa-search search-icon"></i>
            </div>
            <div class="filter-tabs">
                <div class="filter-tab active">全部</div>
                <div class="filter-tab">获奖作品</div>
                <div class="filter-tab">最新上传</div>
                <div class="filter-tab">人气最高</div>
                <div class="filter-tab">动漫角色</div>
                <div class="filter-tab">游戏角色</div>
                <div class="filter-tab">原创角色</div>
            </div>
        </div>

        <div class="works-container">
            <!-- 作品卡片1 -->
            <div class="work-card" onclick="window.location.href='work_detail.html'">
                <div class="work-image">
                    <img src="https://images.unsplash.com/photo-1530071100468-90fe162e2a20?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80"
                        alt="作品">
                    <div class="work-badge gold">金奖</div>
                </div>
                <div class="work-info">
                    <div class="work-title">星之守护者 - 拉克丝</div>
                    <div class="work-author">
                        <i class="fas fa-user-circle"></i>
                        <span>梦幻Cosplayer</span>
                    </div>
                    <div class="work-stats">
                        <div class="stat">
                            <i class="fas fa-heart"></i>
                            <span>1.2k</span>
                        </div>
                        <div class="stat">
                            <i class="fas fa-eye"></i>
                            <span>5.8k</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 作品卡片2 -->
            <div class="work-card" onclick="window.location.href='work_detail.html'">
                <div class="work-image">
                    <img src="https://images.unsplash.com/photo-1563804447127-f4ef4ff5f1d1?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80"
                        alt="作品">
                    <div class="work-badge silver">银奖</div>
                </div>
                <div class="work-info">
                    <div class="work-title">魔卡少女 - 小樱</div>
                    <div class="work-author">
                        <i class="fas fa-user-circle"></i>
                        <span>樱花之舞</span>
                    </div>
                    <div class="work-stats">
                        <div class="stat">
                            <i class="fas fa-heart"></i>
                            <span>986</span>
                        </div>
                        <div class="stat">
                            <i class="fas fa-eye"></i>
                            <span>4.5k</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 作品卡片3 -->
            <div class="work-card" onclick="window.location.href='work_detail.html'">
                <div class="work-image">
                    <img src="https://images.unsplash.com/photo-1566454825481-4a8a4eda5094?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80"
                        alt="作品">
                    <div class="work-badge bronze">铜奖</div>
                </div>
                <div class="work-info">
                    <div class="work-title">最终幻想VII - 蒂法</div>
                    <div class="work-author">
                        <i class="fas fa-user-circle"></i>
                        <span>幻想勇者</span>
                    </div>
                    <div class="work-stats">
                        <div class="stat">
                            <i class="fas fa-heart"></i>
                            <span>875</span>
                        </div>
                        <div class="stat">
                            <i class="fas fa-eye"></i>
                            <span>4.2k</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 作品卡片4 -->
            <div class="work-card" onclick="window.location.href='work_detail.html'">
                <div class="work-image">
                    <img src="https://images.unsplash.com/photo-1591417033176-0ca2633c1bcf?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80"
                        alt="作品">
                </div>
                <div class="work-info">
                    <div class="work-title">原神 - 刻晴</div>
                    <div class="work-author">
                        <i class="fas fa-user-circle"></i>
                        <span>璃月之星</span>
                    </div>
                    <div class="work-stats">
                        <div class="stat">
                            <i class="fas fa-heart"></i>
                            <span>756</span>
                        </div>
                        <div class="stat">
                            <i class="fas fa-eye"></i>
                            <span>3.8k</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 作品卡片5 -->
            <div class="work-card" onclick="window.location.href='work_detail.html'">
                <div class="work-image">
                    <img src="https://images.unsplash.com/photo-1569443693539-175ea9f007e8?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80"
                        alt="作品">
                </div>
                <div class="work-info">
                    <div class="work-title">鬼灭之刃 - 祢豆子</div>
                    <div class="work-author">
                        <i class="fas fa-user-circle"></i>
                        <span>刀锋之舞</span>
                    </div>
                    <div class="work-stats">
                        <div class="stat">
                            <i class="fas fa-heart"></i>
                            <span>683</span>
                        </div>
                        <div class="stat">
                            <i class="fas fa-eye"></i>
                            <span>3.5k</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 作品卡片6 -->
            <div class="work-card" onclick="window.location.href='work_detail.html'">
                <div class="work-image">
                    <img src="https://images.unsplash.com/photo-1604916287784-c324202b3205?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80"
                        alt="作品">
                </div>
                <div class="work-info">
                    <div class="work-title">英雄联盟 - 艾希</div>
                    <div class="work-author">
                        <i class="fas fa-user-circle"></i>
                        <span>冰霜射手</span>
                    </div>
                    <div class="work-stats">
                        <div class="stat">
                            <i class="fas fa-heart"></i>
                            <span>624</span>
                        </div>
                        <div class="stat">
                            <i class="fas fa-eye"></i>
                            <span>3.2k</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 作品卡片7 -->
            <div class="work-card" onclick="window.location.href='work_detail.html'">
                <div class="work-image">
                    <img src="https://images.unsplash.com/photo-1531259683007-016a7b628fc3?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80"
                        alt="作品">
                </div>
                <div class="work-info">
                    <div class="work-title">JOJO奇妙冒险 - 乔瑟夫</div>
                    <div class="work-author">
                        <i class="fas fa-user-circle"></i>
                        <span>替身使者</span>
                    </div>
                    <div class="work-stats">
                        <div class="stat">
                            <i class="fas fa-heart"></i>
                            <span>589</span>
                        </div>
                        <div class="stat">
                            <i class="fas fa-eye"></i>
                            <span>2.9k</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 作品卡片8 -->
            <div class="work-card" onclick="window.location.href='work_detail.html'">
                <div class="work-image">
                    <img src="https://images.unsplash.com/photo-1559087316-f8ac867ebaa6?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80"
                        alt="作品">
                </div>
                <div class="work-info">
                    <div class="work-title">赛博朋克2077 - V</div>
                    <div class="work-author">
                        <i class="fas fa-user-circle"></i>
                        <span>夜之城</span>
                    </div>
                    <div class="work-stats">
                        <div class="stat">
                            <i class="fas fa-heart"></i>
                            <span>547</span>
                        </div>
                        <div class="stat">
                            <i class="fas fa-eye"></i>
                            <span>2.7k</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="load-more">
            加载更多
        </div>

        <div class="filter-button" id="filter-btn">
            <i class="fas fa-sliders-h"></i>
        </div>

        <div class="overlay" id="overlay"></div>

        <div class="filter-drawer" id="filter-drawer">
            <div class="drawer-header">
                <div class="drawer-title">筛选</div>
                <div class="close-button" id="close-btn">
                    <i class="fas fa-times"></i>
                </div>
            </div>

            <div class="filter-section">
                <div class="filter-section-title">作品类型</div>
                <div class="filter-options">
                    <div class="filter-option selected">全部</div>
                    <div class="filter-option">动漫角色</div>
                    <div class="filter-option">游戏角色</div>
                    <div class="filter-option">电影角色</div>
                    <div class="filter-option">原创角色</div>
                </div>
            </div>

            <div class="filter-section">
                <div class="filter-section-title">获奖情况</div>
                <div class="filter-options">
                    <div class="filter-option selected">全部</div>
                    <div class="filter-option">金奖</div>
                    <div class="filter-option">银奖</div>
                    <div class="filter-option">铜奖</div>
                    <div class="filter-option">优胜奖</div>
                    <div class="filter-option">未获奖</div>
                </div>
            </div>

            <div class="filter-section">
                <div class="filter-section-title">上传时间</div>
                <div class="filter-options">
                    <div class="filter-option selected">全部时间</div>
                    <div class="filter-option">本周内</div>
                    <div class="filter-option">本月内</div>
                    <div class="filter-option">三个月内</div>
                    <div class="filter-option">今年内</div>
                </div>
            </div>

            <div class="filter-section">
                <div class="filter-section-title">排序方式</div>
                <div class="filter-options">
                    <div class="filter-option selected">最新上传</div>
                    <div class="filter-option">最多点赞</div>
                    <div class="filter-option">最多浏览</div>
                    <div class="filter-option">最多收藏</div>
                </div>
            </div>

            <div class="filter-actions">
                <div class="filter-action reset" id="reset-btn">重置</div>
                <div class="filter-action apply" id="apply-btn">应用</div>
            </div>
        </div>
    </div>

    <div class="bottom-nav">
        <div class="nav-item" data-target="home">
            <i class="fas fa-home"></i>
            <span>首页</span>
        </div>
        <div class="nav-item" data-target="events">
            <i class="fas fa-calendar-alt"></i>
            <span>活动</span>
        </div>
        <div class="nav-item active" data-target="work_list">
            <i class="fas fa-images"></i>
            <span>作品</span>
        </div>
        <div class="nav-item" data-target="profile">
            <i class="fas fa-user"></i>
            <span>我的</span>
        </div>
    </div>

    <script src="../js/main.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // 更新状态栏时间
            updateStatusBarTime();

            // 筛选Tab切换
            const filterTabs = document.querySelectorAll('.filter-tab');
            filterTabs.forEach(tab => {
                tab.addEventListener('click', function () {
                    filterTabs.forEach(t => t.classList.remove('active'));
                    this.classList.add('active');
                });
            });

            // 筛选抽屉
            const filterBtn = document.getElementById('filter-btn');
            const closeBtn = document.getElementById('close-btn');
            const overlay = document.getElementById('overlay');
            const filterDrawer = document.getElementById('filter-drawer');
            const applyBtn = document.getElementById('apply-btn');
            const resetBtn = document.getElementById('reset-btn');

            // 打开筛选抽屉
            filterBtn.addEventListener('click', function () {
                filterDrawer.classList.add('open');
                overlay.classList.add('show');
                document.body.style.overflow = 'hidden';
            });

            // 关闭筛选抽屉
            function closeDrawer() {
                filterDrawer.classList.remove('open');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            }

            closeBtn.addEventListener('click', closeDrawer);
            overlay.addEventListener('click', closeDrawer);
            applyBtn.addEventListener('click', closeDrawer);

            // 筛选选项切换
            const filterOptions = document.querySelectorAll('.filter-option');
            filterOptions.forEach(option => {
                option.addEventListener('click', function () {
                    const parent = this.parentElement;
                    parent.querySelectorAll('.filter-option').forEach(opt => {
                        if (opt !== this) {
                            opt.classList.remove('selected');
                        }
                    });
                    this.classList.toggle('selected');
                });
            });

            // 重置筛选
            resetBtn.addEventListener('click', function () {
                document.querySelectorAll('.filter-options').forEach(options => {
                    options.querySelectorAll('.filter-option').forEach((opt, index) => {
                        if (index === 0) {
                            opt.classList.add('selected');
                        } else {
                            opt.classList.remove('selected');
                        }
                    });
                });
            });

            // 加载更多
            const loadMore = document.querySelector('.load-more');
            loadMore.addEventListener('click', function () {
                this.textContent = '加载中...';
                // 模拟加载
                setTimeout(() => {
                    this.textContent = '加载更多';
                }, 1500);
            });
        });
    </script>
</body>

</html>
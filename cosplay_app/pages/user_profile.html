<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人资料 - Cosplay大赛</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../css/main.css">
    <link rel="stylesheet" href="../css/responsive.css">
    <style>
        .profile-edit-page {
            padding-bottom: 80px;
        }

        .page-header {
            background: linear-gradient(45deg, var(--primary), #6A1B9A);
            color: white;
            padding: 25px 20px;
            border-radius: 0 0 20px 20px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            position: relative;
            margin-bottom: 20px;
        }

        .header-content {
            width: 100%;
            text-align: center;
        }

        .page-title {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .avatar-section {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-bottom: 20px;
        }

        .avatar-container {
            position: relative;
            width: 100px;
            height: 100px;
            margin-bottom: 10px;
        }

        .avatar {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid white;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        }

        .avatar-edit {
            position: absolute;
            bottom: 0;
            right: 0;
            width: 32px;
            height: 32px;
            background: var(--primary);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
            cursor: pointer;
        }

        .avatar-upload {
            display: none;
        }

        .profile-form {
            padding: 0 20px;
        }

        .form-section {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }

        .section-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 15px;
            color: var(--gray-800);
            display: flex;
            align-items: center;
        }

        .section-title i {
            margin-right: 10px;
            color: var(--primary);
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group:last-child {
            margin-bottom: 0;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-size: 14px;
            color: var(--gray-700);
            font-weight: 500;
        }

        .input-with-icon {
            position: relative;
        }

        .input-with-icon .form-control {
            padding-left: 40px;
        }

        .input-with-icon i {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--gray-500);
        }

        .form-note {
            font-size: 12px;
            color: var(--gray-500);
            margin-top: 5px;
        }

        .toggle-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .toggle-label {
            font-size: 14px;
            color: var(--gray-700);
        }

        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
        }

        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .toggle-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: var(--gray-300);
            transition: .4s;
            border-radius: 24px;
        }

        .toggle-slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked+.toggle-slider {
            background-color: var(--primary);
        }

        input:checked+.toggle-slider:before {
            transform: translateX(26px);
        }

        .divider {
            height: 1px;
            background: var(--gray-200);
            margin: 15px 0;
        }

        .save-button {
            background: linear-gradient(45deg, var(--primary), #6A1B9A);
            color: white;
            padding: 15px;
            border-radius: 12px;
            text-align: center;
            font-size: 16px;
            font-weight: bold;
            box-shadow: 0 4px 15px rgba(138, 43, 226, 0.3);
            cursor: pointer;
            transition: all 0.2s;
            margin-top: 20px;
            width: 100%;
        }

        .save-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(138, 43, 226, 0.4);
        }

        .back-button {
            position: absolute;
            top: 15px;
            left: 15px;
            z-index: 10;
            width: 36px;
            height: 36px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            backdrop-filter: blur(5px);
        }

        .radio-options {
            display: flex;
            gap: 15px;
        }

        .radio-option {
            flex: 1;
            background: var(--gray-100);
            border: 1px solid var(--gray-300);
            border-radius: 10px;
            padding: 10px;
            text-align: center;
            font-size: 14px;
            color: var(--gray-700);
            cursor: pointer;
            transition: all 0.2s;
        }

        .radio-option.selected {
            background: rgba(138, 43, 226, 0.1);
            border-color: var(--primary);
            color: var(--primary);
            font-weight: 500;
        }
    </style>
</head>

<body>
    <div class="status-bar">
        <div class="status-bar-notch"></div>
        <div class="status-bar-left">
            <span>中国移动</span>
            <i class="fas fa-signal"></i>
        </div>
        <div class="status-bar-center status-bar-time">14:30</div>
        <div class="status-bar-right">
            <i class="fas fa-wifi"></i>
            <i class="fas fa-battery-three-quarters"></i>
        </div>
    </div>

    <div class="app-content">
        <div class="profile-edit-page">
            <div class="page-header">
                <div class="back-button" onclick="window.location.href='profile.html'">
                    <i class="fas fa-chevron-left"></i>
                </div>
                <div class="header-content">
                    <div class="page-title">个人资料</div>
                </div>
            </div>

            <div class="avatar-section">
                <div class="avatar-container">
                    <img src="https://i.pravatar.cc/200?img=35" alt="用户头像" class="avatar">
                    <div class="avatar-edit" onclick="document.getElementById('avatar-upload').click()">
                        <i class="fas fa-camera"></i>
                    </div>
                    <input type="file" id="avatar-upload" class="avatar-upload" accept="image/*"
                        onchange="previewAvatar(this)">
                </div>
                <div class="user-name">梦幻Cosplayer</div>
            </div>

            <div class="profile-form">
                <div class="form-section">
                    <div class="section-title">
                        <i class="fas fa-user"></i>
                        基本信息
                    </div>
                    <div class="form-group">
                        <label class="form-label">昵称</label>
                        <div class="input-with-icon">
                            <input type="text" class="form-control" value="梦幻Cosplayer">
                            <i class="fas fa-user-tag"></i>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">性别</label>
                        <div class="radio-options">
                            <div class="radio-option" onclick="selectGender(this)">男</div>
                            <div class="radio-option selected" onclick="selectGender(this)">女</div>
                            <div class="radio-option" onclick="selectGender(this)">保密</div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">生日</label>
                        <div class="input-with-icon">
                            <input type="date" class="form-control" value="1998-06-15">
                            <i class="fas fa-birthday-cake"></i>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">个人简介</label>
                        <textarea class="form-control" rows="3"
                            placeholder="介绍一下自己吧...">热爱cosplay的动漫爱好者，擅长制作精美服装和道具，曾获得全国高校cosplay大赛铜奖。</textarea>
                    </div>
                </div>

                <div class="form-section">
                    <div class="section-title">
                        <i class="fas fa-address-card"></i>
                        联系方式
                    </div>
                    <div class="form-group">
                        <label class="form-label">手机号码</label>
                        <div class="input-with-icon">
                            <input type="tel" class="form-control" value="138****6789">
                            <i class="fas fa-mobile-alt"></i>
                        </div>
                        <div class="form-note">手机号用于登录和接收通知，不会公开显示</div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">电子邮箱</label>
                        <div class="input-with-icon">
                            <input type="email" class="form-control" value="<EMAIL>">
                            <i class="fas fa-envelope"></i>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">微信</label>
                        <div class="input-with-icon">
                            <input type="text" class="form-control" placeholder="绑定微信号">
                            <i class="fab fa-weixin"></i>
                        </div>
                    </div>
                </div>

                <div class="form-section">
                    <div class="section-title">
                        <i class="fas fa-cog"></i>
                        隐私设置
                    </div>
                    <div class="toggle-container">
                        <div class="toggle-label">允许其他用户查看我的作品</div>
                        <label class="toggle-switch">
                            <input type="checkbox" checked>
                            <span class="toggle-slider"></span>
                        </label>
                    </div>
                    <div class="divider"></div>
                    <div class="toggle-container">
                        <div class="toggle-label">接收活动提醒</div>
                        <label class="toggle-switch">
                            <input type="checkbox" checked>
                            <span class="toggle-slider"></span>
                        </label>
                    </div>
                    <div class="divider"></div>
                    <div class="toggle-container">
                        <div class="toggle-label">公开我的个人资料</div>
                        <label class="toggle-switch">
                            <input type="checkbox">
                            <span class="toggle-slider"></span>
                        </label>
                    </div>
                </div>

                <div class="save-button" onclick="saveProfile()">
                    保存修改
                </div>
            </div>
        </div>
    </div>

    <div class="bottom-nav">
        <div class="nav-item" data-target="home" onclick="window.location.href='home.html'">
            <i class="fas fa-home"></i>
            <span>首页</span>
        </div>
        <div class="nav-item active" data-target="profile" onclick="window.location.href='profile.html'">
            <i class="fas fa-user"></i>
            <span>我的</span>
        </div>
    </div>

    <script src="../js/main.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // 更新状态栏时间
            updateStatusBarTime();
        });

        // 预览头像
        function previewAvatar(input) {
            if (input.files && input.files[0]) {
                const reader = new FileReader();

                reader.onload = function (e) {
                    document.querySelector('.avatar').src = e.target.result;
                };

                reader.readAsDataURL(input.files[0]);
            }
        }

        // 选择性别
        function selectGender(element) {
            const options = document.querySelectorAll('.radio-option');
            options.forEach(option => {
                option.classList.remove('selected');
            });

            element.classList.add('selected');
        }

        // 保存个人资料
        function saveProfile() {
            // 显示加载动画或提示
            showToast('保存成功', 'success');

            // 模拟保存延迟
            setTimeout(function () {
                window.location.href = 'profile.html';
            }, 1500);
        }

        // 显示消息提示
        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.className = `toast toast-${type}`;
            toast.innerHTML = `
                <div class="toast-icon">
                    <i class="fas ${type === 'success' ? 'fa-check-circle' : 'fa-info-circle'}"></i>
                </div>
                <div class="toast-message">${message}</div>
            `;

            // 添加样式
            toast.style.position = 'fixed';
            toast.style.bottom = '80px';
            toast.style.left = '50%';
            toast.style.transform = 'translateX(-50%)';
            toast.style.background = type === 'success' ? '#38a169' : '#3182ce';
            toast.style.color = 'white';
            toast.style.padding = '10px 20px';
            toast.style.borderRadius = '10px';
            toast.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.1)';
            toast.style.display = 'flex';
            toast.style.alignItems = 'center';
            toast.style.zIndex = '1000';

            // 图标样式
            toast.querySelector('.toast-icon').style.marginRight = '10px';

            document.body.appendChild(toast);

            // 自动消失
            setTimeout(() => {
                toast.style.opacity = '0';
                toast.style.transition = 'opacity 0.5s';
                setTimeout(() => {
                    document.body.removeChild(toast);
                }, 500);
            }, 2000);
        }
    </script>
</body>

</html>
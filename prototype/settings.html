<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>萌宠记 - 设置</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
    <style>
        /* 设置页面特定样式 */
        .settings-header {
            display: flex;
            align-items: center;
            padding: 16px;
            background: var(--background);
            border-bottom: 1px solid var(--border-color);
        }

        .back-btn {
            background: none;
            border: none;
            font-size: 18px;
            color: var(--text-primary);
            cursor: pointer;
            margin-right: 16px;
        }

        .header-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
        }

        .settings-content {
            background: var(--background-secondary);
        }

        .settings-section {
            background: var(--background);
            margin-bottom: 12px;
        }

        .section-title {
            padding: 16px 16px 8px;
            font-size: 12px;
            font-weight: 600;
            color: var(--text-secondary);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .settings-item {
            display: flex;
            align-items: center;
            padding: 16px;
            border-bottom: 1px solid var(--border-color);
            cursor: pointer;
            transition: background 0.2s;
        }

        .settings-item:last-child {
            border-bottom: none;
        }

        .settings-item:hover {
            background: var(--background-secondary);
        }

        .item-icon {
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            color: var(--primary-color);
            font-size: 16px;
        }

        .item-content {
            flex: 1;
        }

        .item-title {
            font-size: 16px;
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 2px;
        }

        .item-desc {
            font-size: 12px;
            color: var(--text-secondary);
            line-height: 1.3;
        }

        .item-value {
            font-size: 14px;
            color: var(--text-secondary);
            margin-right: 8px;
        }

        .item-arrow {
            color: var(--text-secondary);
            font-size: 12px;
        }

        .item-switch {
            position: relative;
            width: 44px;
            height: 24px;
            background: var(--border-color);
            border-radius: 12px;
            cursor: pointer;
            transition: background 0.2s;
        }

        .item-switch.active {
            background: var(--primary-color);
        }

        .switch-handle {
            position: absolute;
            top: 2px;
            left: 2px;
            width: 20px;
            height: 20px;
            background: white;
            border-radius: 50%;
            transition: transform 0.2s;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .item-switch.active .switch-handle {
            transform: translateX(20px);
        }

        .item-badge {
            background: var(--primary-color);
            color: white;
            border-radius: 10px;
            padding: 2px 6px;
            font-size: 10px;
            font-weight: 600;
            margin-right: 8px;
        }

        .danger-item {
            color: #e74c3c !important;
        }

        .danger-item .item-icon {
            color: #e74c3c !important;
        }

        .danger-item .item-title {
            color: #e74c3c !important;
        }

        .version-info {
            text-align: center;
            padding: 32px 16px;
            color: var(--text-secondary);
        }

        .app-logo {
            width: 64px;
            height: 64px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 16px;
            color: white;
            font-size: 24px;
        }

        .app-name {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 4px;
        }

        .app-version {
            font-size: 14px;
            color: var(--text-secondary);
            margin-bottom: 8px;
        }

        .copyright {
            font-size: 12px;
            color: var(--text-secondary);
        }

        /* 弹窗样式 */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        .modal-content {
            background: var(--background);
            border-radius: 16px;
            padding: 24px;
            margin: 20px;
            max-width: 300px;
            text-align: center;
        }

        .modal-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 12px;
        }

        .modal-text {
            font-size: 14px;
            color: var(--text-secondary);
            line-height: 1.4;
            margin-bottom: 24px;
        }

        .modal-buttons {
            display: flex;
            gap: 12px;
        }

        .modal-btn {
            flex: 1;
            padding: 12px;
            border: none;
            border-radius: 12px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
        }

        .modal-btn.cancel {
            background: var(--background-secondary);
            color: var(--text-primary);
        }

        .modal-btn.confirm {
            background: #e74c3c;
            color: white;
        }
    </style>
</head>

<body>
    <div class="iphone-container">
        <!-- iOS 状态栏 -->
        <div class="status-bar">
            <div class="status-bar-notch"></div>
            <div class="status-bar-left">
                <span>中国移动</span>
                <i class="fas fa-signal"></i>
            </div>
            <div class="status-bar-center status-bar-time">9:41</div>
            <div class="status-bar-right">
                <i class="fas fa-wifi"></i>
                <i class="fas fa-battery-three-quarters"></i>
            </div>
        </div>

        <!-- 主内容区域 -->
        <div class="main-content">
            <!-- 设置页面头部 -->
            <div class="settings-header">
                <button class="back-btn" onclick="window.location.href='profile.html'">
                    <i class="fas fa-arrow-left"></i>
                </button>
                <h1 class="header-title">设置</h1>
            </div>

            <div class="settings-content">
                <!-- 账号设置 -->
                <div class="settings-section">
                    <div class="section-title">账号设置</div>

                    <div class="settings-item">
                        <div class="item-icon">
                            <i class="fas fa-user-edit"></i>
                        </div>
                        <div class="item-content">
                            <div class="item-title">编辑资料</div>
                            <div class="item-desc">修改头像、昵称、个人简介</div>
                        </div>
                        <i class="fas fa-chevron-right item-arrow"></i>
                    </div>

                    <div class="settings-item">
                        <div class="item-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <div class="item-content">
                            <div class="item-title">隐私设置</div>
                            <div class="item-desc">作品可见范围、黑名单管理</div>
                        </div>
                        <i class="fas fa-chevron-right item-arrow"></i>
                    </div>

                    <div class="settings-item">
                        <div class="item-icon">
                            <i class="fas fa-key"></i>
                        </div>
                        <div class="item-content">
                            <div class="item-title">账号安全</div>
                            <div class="item-desc">密码修改、手机号绑定</div>
                        </div>
                        <i class="fas fa-chevron-right item-arrow"></i>
                    </div>
                </div>

                <!-- 通知设置 -->
                <div class="settings-section">
                    <div class="section-title">通知设置</div>

                    <div class="settings-item">
                        <div class="item-icon">
                            <i class="fas fa-bell"></i>
                        </div>
                        <div class="item-content">
                            <div class="item-title">推送通知</div>
                            <div class="item-desc">接收点赞、评论、关注通知</div>
                        </div>
                        <div class="item-switch active">
                            <div class="switch-handle"></div>
                        </div>
                    </div>

                    <div class="settings-item">
                        <div class="item-icon">
                            <i class="fas fa-seedling"></i>
                        </div>
                        <div class="item-content">
                            <div class="item-title">献花通知</div>
                            <div class="item-desc">宠物收到献花时通知</div>
                        </div>
                        <div class="item-switch active">
                            <div class="switch-handle"></div>
                        </div>
                    </div>

                    <div class="settings-item">
                        <div class="item-icon">
                            <i class="fas fa-volume-up"></i>
                        </div>
                        <div class="item-content">
                            <div class="item-title">声音提醒</div>
                            <div class="item-desc">通知声音和震动</div>
                        </div>
                        <div class="item-switch">
                            <div class="switch-handle"></div>
                        </div>
                    </div>
                </div>

                <!-- 内容设置 -->
                <div class="settings-section">
                    <div class="section-title">内容设置</div>

                    <div class="settings-item">
                        <div class="item-icon">
                            <i class="fas fa-eye"></i>
                        </div>
                        <div class="item-content">
                            <div class="item-title">默认可见范围</div>
                            <div class="item-desc">新发布内容的默认可见范围</div>
                        </div>
                        <div class="item-value">公开</div>
                        <i class="fas fa-chevron-right item-arrow"></i>
                    </div>

                    <div class="settings-item">
                        <div class="item-icon">
                            <i class="fas fa-download"></i>
                        </div>
                        <div class="item-content">
                            <div class="item-title">自动保存草稿</div>
                            <div class="item-desc">编辑时自动保存到草稿箱</div>
                        </div>
                        <div class="item-switch active">
                            <div class="switch-handle"></div>
                        </div>
                    </div>

                    <div class="settings-item">
                        <div class="item-icon">
                            <i class="fas fa-wifi"></i>
                        </div>
                        <div class="item-content">
                            <div class="item-title">仅WiFi上传</div>
                            <div class="item-desc">仅在WiFi环境下上传图片视频</div>
                        </div>
                        <div class="item-switch">
                            <div class="switch-handle"></div>
                        </div>
                    </div>
                </div>

                <!-- 其他设置 -->
                <div class="settings-section">
                    <div class="section-title">其他</div>

                    <div class="settings-item">
                        <div class="item-icon">
                            <i class="fas fa-question-circle"></i>
                        </div>
                        <div class="item-content">
                            <div class="item-title">帮助中心</div>
                            <div class="item-desc">常见问题、使用指南</div>
                        </div>
                        <i class="fas fa-chevron-right item-arrow"></i>
                    </div>

                    <div class="settings-item">
                        <div class="item-icon">
                            <i class="fas fa-comment-alt"></i>
                        </div>
                        <div class="item-content">
                            <div class="item-title">意见反馈</div>
                            <div class="item-desc">问题反馈、功能建议</div>
                        </div>
                        <div class="item-badge">NEW</div>
                        <i class="fas fa-chevron-right item-arrow"></i>
                    </div>

                    <div class="settings-item">
                        <div class="item-icon">
                            <i class="fas fa-file-alt"></i>
                        </div>
                        <div class="item-content">
                            <div class="item-title">用户协议</div>
                            <div class="item-desc">服务条款、隐私政策</div>
                        </div>
                        <i class="fas fa-chevron-right item-arrow"></i>
                    </div>

                    <div class="settings-item">
                        <div class="item-icon">
                            <i class="fas fa-info-circle"></i>
                        </div>
                        <div class="item-content">
                            <div class="item-title">关于萌宠记</div>
                            <div class="item-desc">版本信息、开发团队</div>
                        </div>
                        <i class="fas fa-chevron-right item-arrow"></i>
                    </div>
                </div>

                <!-- 危险操作 -->
                <div class="settings-section">
                    <div class="settings-item danger-item">
                        <div class="item-icon">
                            <i class="fas fa-sign-out-alt"></i>
                        </div>
                        <div class="item-content">
                            <div class="item-title">退出登录</div>
                        </div>
                        <i class="fas fa-chevron-right item-arrow"></i>
                    </div>
                </div>

                <!-- 版本信息 -->
                <div class="version-info">
                    <div class="app-logo">
                        <i class="fas fa-paw"></i>
                    </div>
                    <div class="app-name">萌宠记</div>
                    <div class="app-version">版本 1.0.0</div>
                    <div class="copyright">© 2024 PetMoments. All rights reserved.</div>
                </div>
            </div>
        </div>

        <!-- 底部导航栏 -->
        <div class="tab-bar">
            <a href="home.html" class="tab-item">
                <i class="fas fa-home"></i>
                <span>主页</span>
            </a>
            <a href="topics.html" class="tab-item">
                <i class="fas fa-hashtag"></i>
                <span>话题</span>
            </a>
            <a href="create.html" class="tab-item">
                <i class="fas fa-plus-circle"></i>
                <span>创作</span>
            </a>
            <a href="messages.html" class="tab-item">
                <i class="fas fa-bell"></i>
                <span>消息</span>
            </a>
            <a href="profile.html" class="tab-item active">
                <i class="fas fa-user"></i>
                <span>我的</span>
            </a>
        </div>
    </div>

    <!-- 退出登录确认弹窗 -->
    <div class="modal" id="logout-modal">
        <div class="modal-content">
            <div class="modal-title">退出登录</div>
            <div class="modal-text">确定要退出当前账号吗？退出后需要重新登录才能使用完整功能。</div>
            <div class="modal-buttons">
                <button class="modal-btn cancel" onclick="closeModal()">取消</button>
                <button class="modal-btn confirm" onclick="confirmLogout()">退出</button>
            </div>
        </div>
    </div>

    <script>
        // 开关切换
        document.querySelectorAll('.item-switch').forEach(switchEl => {
            switchEl.addEventListener('click', function () {
                this.classList.toggle('active');
            });
        });

        // 设置项点击
        document.querySelectorAll('.settings-item').forEach(item => {
            item.addEventListener('click', function () {
                if (this.classList.contains('danger-item')) {
                    // 退出登录
                    document.getElementById('logout-modal').style.display = 'flex';
                } else if (!this.querySelector('.item-switch')) {
                    // 其他设置项的点击效果
                    this.style.backgroundColor = 'var(--background-secondary)';
                    setTimeout(() => {
                        this.style.backgroundColor = '';
                    }, 200);
                }
            });
        });

        function closeModal() {
            document.getElementById('logout-modal').style.display = 'none';
        }

        function confirmLogout() {
            closeModal();
            alert('已退出登录');
            // 这里应该清除登录状态并跳转到登录页
        }

        // 点击弹窗背景关闭
        document.getElementById('logout-modal').addEventListener('click', function (e) {
            if (e.target === this) {
                closeModal();
            }
        });

        // 返回按钮
        document.querySelector('.back-btn').addEventListener('click', function () {
            this.style.transform = 'scale(0.9)';
            setTimeout(() => {
                this.style.transform = 'scale(1)';
            }, 150);
        });
    </script>
</body>

</html>
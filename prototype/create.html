<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>萌宠记 - 创作发布</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
    <style>
        /* 创作页面特定样式 */
        .create-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 16px;
            background: var(--background);
            border-bottom: 1px solid var(--border-color);
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .back-btn {
            background: none;
            border: none;
            font-size: 18px;
            color: var(--text-primary);
            cursor: pointer;
        }

        .header-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
        }

        .publish-btn {
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 16px;
            padding: 8px 20px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
        }

        .publish-btn:disabled {
            background: var(--text-secondary);
            cursor: not-allowed;
        }

        .create-content {
            padding: 16px;
        }

        .media-upload {
            background: var(--background-secondary);
            border: 2px dashed var(--border-color);
            border-radius: 16px;
            padding: 40px 20px;
            text-align: center;
            margin-bottom: 20px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .media-upload:hover {
            border-color: var(--primary-color);
            background: rgba(255, 107, 107, 0.05);
        }

        .upload-icon {
            font-size: 48px;
            color: var(--text-secondary);
            margin-bottom: 12px;
        }

        .upload-text {
            font-size: 16px;
            color: var(--text-primary);
            font-weight: 500;
            margin-bottom: 4px;
        }

        .upload-hint {
            font-size: 12px;
            color: var(--text-secondary);
        }

        .media-preview {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 8px;
            margin-bottom: 20px;
        }

        .preview-item {
            position: relative;
            aspect-ratio: 1;
            border-radius: 12px;
            overflow: hidden;
        }

        .preview-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .remove-btn {
            position: absolute;
            top: 4px;
            right: 4px;
            width: 24px;
            height: 24px;
            background: rgba(0, 0, 0, 0.6);
            color: white;
            border: none;
            border-radius: 50%;
            font-size: 12px;
            cursor: pointer;
        }

        .text-input {
            width: 100%;
            min-height: 120px;
            padding: 16px;
            border: 1px solid var(--border-color);
            border-radius: 16px;
            font-size: 16px;
            line-height: 1.5;
            resize: none;
            outline: none;
            margin-bottom: 20px;
            font-family: inherit;
        }

        .text-input:focus {
            border-color: var(--primary-color);
        }

        .text-input::placeholder {
            color: var(--text-secondary);
        }

        .section {
            margin-bottom: 24px;
        }

        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .pet-selector {
            display: flex;
            gap: 12px;
            overflow-x: auto;
            padding-bottom: 8px;
        }

        .pet-option {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
            padding: 12px;
            background: var(--background);
            border: 2px solid var(--border-color);
            border-radius: 16px;
            cursor: pointer;
            transition: all 0.2s;
            min-width: 80px;
        }

        .pet-option.selected {
            border-color: var(--primary-color);
            background: rgba(255, 107, 107, 0.1);
        }

        .pet-option img {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            object-fit: cover;
        }

        .pet-option span {
            font-size: 12px;
            font-weight: 500;
            color: var(--text-primary);
            text-align: center;
        }

        .add-pet {
            background: var(--background-secondary);
            border: 2px dashed var(--border-color);
            color: var(--text-secondary);
        }

        .add-pet i {
            font-size: 24px;
            margin-bottom: 4px;
        }

        .topic-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .tag-input {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 12px;
        }

        .tag-input input {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid var(--border-color);
            border-radius: 20px;
            font-size: 14px;
            outline: none;
        }

        .add-tag-btn {
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 50%;
            width: 32px;
            height: 32px;
            font-size: 14px;
            cursor: pointer;
        }

        .tag-item {
            background: var(--background-secondary);
            color: var(--text-primary);
            padding: 6px 12px;
            border-radius: 16px;
            font-size: 12px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .tag-item .remove {
            cursor: pointer;
            color: var(--text-secondary);
        }

        .suggested-tags {
            margin-top: 12px;
        }

        .suggested-tag {
            background: var(--background);
            color: var(--text-secondary);
            border: 1px solid var(--border-color);
            padding: 6px 12px;
            border-radius: 16px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .suggested-tag:hover {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .privacy-settings {
            background: var(--background);
            border-radius: 16px;
            padding: 16px;
            border: 1px solid var(--border-color);
        }

        .privacy-option {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 0;
            border-bottom: 1px solid var(--border-color);
        }

        .privacy-option:last-child {
            border-bottom: none;
        }

        .privacy-info {
            flex: 1;
        }

        .privacy-title {
            font-size: 14px;
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 2px;
        }

        .privacy-desc {
            font-size: 12px;
            color: var(--text-secondary);
        }

        .radio-btn {
            width: 20px;
            height: 20px;
            border: 2px solid var(--border-color);
            border-radius: 50%;
            position: relative;
            cursor: pointer;
        }

        .radio-btn.selected {
            border-color: var(--primary-color);
        }

        .radio-btn.selected::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 10px;
            height: 10px;
            background: var(--primary-color);
            border-radius: 50%;
        }
    </style>
</head>

<body>
    <div class="iphone-container">
        <!-- iOS 状态栏 -->
        <div class="status-bar">
            <div class="status-left">
                <span>9:41</span>
            </div>
            <div class="status-right">
                <i class="fas fa-signal"></i>
                <i class="fas fa-wifi"></i>
                <div class="battery">
                    <div class="battery-fill"></div>
                </div>
            </div>
        </div>

        <!-- 主内容区域 -->
        <div class="main-content">
            <!-- 创作页面头部 -->
            <div class="create-header">
                <div class="header-left">
                    <button class="back-btn" onclick="window.location.href='home.html'">
                        <i class="fas fa-arrow-left"></i>
                    </button>
                    <h1 class="header-title">创作发布</h1>
                </div>
                <button class="publish-btn ripple-effect" disabled>发布</button>
            </div>

            <div class="create-content">
                <!-- 媒体上传区域 -->
                <div class="media-upload" onclick="document.getElementById('media-input').click()">
                    <div class="upload-icon">
                        <i class="fas fa-camera"></i>
                    </div>
                    <div class="upload-text">添加照片或视频</div>
                    <div class="upload-hint">最多可添加9张图片或1个视频</div>
                    <input type="file" id="media-input" multiple accept="image/*,video/*" style="display: none;">
                </div>

                <!-- 媒体预览区域 -->
                <div class="media-preview" id="media-preview" style="display: none;">
                    <!-- 预览项将通过JavaScript动态添加 -->
                </div>

                <!-- 文本输入 -->
                <textarea class="text-input" placeholder="分享你和毛孩子的美好时光...&#10;&#10;💡 小贴士：添加话题标签可以让更多人看到你的内容哦！"
                    id="content-text"></textarea>

                <!-- 关联宠物 -->
                <div class="section">
                    <h3 class="section-title">
                        <i class="fas fa-paw"></i>
                        关联宠物
                    </h3>
                    <div class="pet-selector">
                        <div class="pet-option" data-pet="ball">
                            <img src="https://images.unsplash.com/photo-1552053831-71594a27632d?w=96&h=96&fit=crop&crop=face"
                                alt="球球">
                            <span>球球</span>
                        </div>
                        <div class="pet-option" data-pet="orange">
                            <img src="https://images.unsplash.com/photo-1574158622682-e40e69881006?w=96&h=96&fit=crop&crop=face"
                                alt="小橘">
                            <span>小橘</span>
                        </div>
                        <div class="pet-option add-pet" onclick="window.location.href='add-pet.html'">
                            <i class="fas fa-plus"></i>
                            <span>添加宠物</span>
                        </div>
                    </div>
                </div>

                <!-- 话题标签 -->
                <div class="section">
                    <h3 class="section-title">
                        <i class="fas fa-hashtag"></i>
                        话题标签
                    </h3>
                    <div class="tag-input">
                        <input type="text" placeholder="输入话题标签..." id="tag-input">
                        <button class="add-tag-btn" onclick="addTag()">
                            <i class="fas fa-plus"></i>
                        </button>
                    </div>
                    <div class="topic-tags" id="selected-tags">
                        <!-- 已选择的标签将显示在这里 -->
                    </div>
                    <div class="suggested-tags">
                        <span class="suggested-tag" onclick="addSuggestedTag(this)">#萌宠日常</span>
                        <span class="suggested-tag" onclick="addSuggestedTag(this)">#成长记录</span>
                        <span class="suggested-tag" onclick="addSuggestedTag(this)">#金毛</span>
                        <span class="suggested-tag" onclick="addSuggestedTag(this)">#橘猫</span>
                        <span class="suggested-tag" onclick="addSuggestedTag(this)">#公园遛狗</span>
                        <span class="suggested-tag" onclick="addSuggestedTag(this)">#宠物美容</span>
                    </div>
                </div>

                <!-- 隐私设置 -->
                <div class="section">
                    <h3 class="section-title">
                        <i class="fas fa-shield-alt"></i>
                        隐私设置
                    </h3>
                    <div class="privacy-settings">
                        <div class="privacy-option">
                            <div class="privacy-info">
                                <div class="privacy-title">公开</div>
                                <div class="privacy-desc">所有人都可以看到</div>
                            </div>
                            <div class="radio-btn selected" data-privacy="public"></div>
                        </div>
                        <div class="privacy-option">
                            <div class="privacy-info">
                                <div class="privacy-title">仅互关好友</div>
                                <div class="privacy-desc">只有互相关注的用户可见</div>
                            </div>
                            <div class="radio-btn" data-privacy="friends"></div>
                        </div>
                        <div class="privacy-option">
                            <div class="privacy-info">
                                <div class="privacy-title">私密</div>
                                <div class="privacy-desc">仅自己可见</div>
                            </div>
                            <div class="radio-btn" data-privacy="private"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部导航栏 -->
        <div class="tab-bar">
            <a href="#" class="tab-item">
                <i class="fas fa-home"></i>
                <span>主页</span>
            </a>
            <a href="#" class="tab-item">
                <i class="fas fa-hashtag"></i>
                <span>话题</span>
            </a>
            <a href="#" class="tab-item active">
                <i class="fas fa-plus-circle"></i>
                <span>创作</span>
            </a>
            <a href="#" class="tab-item">
                <i class="fas fa-bell"></i>
                <span>消息</span>
            </a>
            <a href="#" class="tab-item">
                <i class="fas fa-user"></i>
                <span>我的</span>
            </a>
        </div>
    </div>

    <script>
        let selectedTags = [];
        let selectedPet = null;
        let selectedPrivacy = 'public';

        // 宠物选择
        document.querySelectorAll('.pet-option[data-pet]').forEach(option => {
            option.addEventListener('click', function () {
                document.querySelectorAll('.pet-option').forEach(opt => opt.classList.remove('selected'));
                this.classList.add('selected');
                selectedPet = this.dataset.pet;
                checkPublishReady();
            });
        });

        // 隐私设置
        document.querySelectorAll('.radio-btn').forEach(btn => {
            btn.addEventListener('click', function () {
                document.querySelectorAll('.radio-btn').forEach(radio => radio.classList.remove('selected'));
                this.classList.add('selected');
                selectedPrivacy = this.dataset.privacy;
            });
        });

        // 添加标签
        function addTag() {
            const input = document.getElementById('tag-input');
            const tag = input.value.trim();
            if (tag && !selectedTags.includes(tag)) {
                selectedTags.push(tag);
                renderTags();
                input.value = '';
                checkPublishReady();
            }
        }

        function addSuggestedTag(element) {
            const tag = element.textContent;
            if (!selectedTags.includes(tag)) {
                selectedTags.push(tag);
                renderTags();
                checkPublishReady();
            }
        }

        function removeTag(tag) {
            selectedTags = selectedTags.filter(t => t !== tag);
            renderTags();
            checkPublishReady();
        }

        function renderTags() {
            const container = document.getElementById('selected-tags');
            container.innerHTML = selectedTags.map(tag => `
                <div class="tag-item">
                    ${tag}
                    <span class="remove" onclick="removeTag('${tag}')">
                        <i class="fas fa-times"></i>
                    </span>
                </div>
            `).join('');
        }

        // 检查是否可以发布
        function checkPublishReady() {
            const contentText = document.getElementById('content-text').value.trim();
            const publishBtn = document.querySelector('.publish-btn');

            if (contentText.length > 0 || selectedTags.length > 0) {
                publishBtn.disabled = false;
                publishBtn.style.background = 'var(--primary-color)';
            } else {
                publishBtn.disabled = true;
                publishBtn.style.background = 'var(--text-secondary)';
            }
        }

        // 文本输入监听
        document.getElementById('content-text').addEventListener('input', checkPublishReady);

        // 标签输入回车键
        document.getElementById('tag-input').addEventListener('keypress', function (e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                addTag();
            }
        });

        // 媒体文件上传处理
        document.getElementById('media-input').addEventListener('change', function (e) {
            const files = Array.from(e.target.files);
            if (files.length > 0) {
                // 这里应该处理文件上传和预览
                // 为了演示，我们显示预览区域
                document.getElementById('media-preview').style.display = 'grid';
                document.querySelector('.media-upload').style.display = 'none';
                checkPublishReady();
            }
        });

        // 发布按钮点击
        document.querySelector('.publish-btn').addEventListener('click', function () {
            if (!this.disabled) {
                // 发布逻辑
                alert('发布成功！');
            }
        });
    </script>
</body>

</html>
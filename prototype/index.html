<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>萌宠记 PetMoments - 原型预览 (改造版)</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #9a9a9a 0%, #393939 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1800px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            color: white;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 700;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
            margin-bottom: 20px;
        }

        .app-info {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 20px;
            margin-bottom: 30px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .app-names {
            display: flex;
            justify-content: center;
            gap: 40px;
            margin-bottom: 15px;
        }

        .app-name {
            text-align: center;
        }

        .app-name h3 {
            color: #FFF3C4;
            font-size: 1.3rem;
            margin-bottom: 5px;
        }

        .app-name p {
            color: rgba(255, 255, 255, 0.8);
            font-size: 0.9rem;
        }

        .prototype-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(410px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .prototype-item {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            transition: transform 0.3s ease;
            min-height: 700px;
        }

        .prototype-item:hover {
            transform: translateY(-5px);
        }

        .prototype-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 15px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f0f0f0;
        }

        .prototype-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #FFB74D, #FFCC02);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
        }

        .prototype-title {
            flex: 1;
        }

        .prototype-title h3 {
            color: #2C3E50;
            font-size: 1.2rem;
            margin-bottom: 4px;
        }

        .prototype-title p {
            color: #7F8C8D;
            font-size: 0.9rem;
        }

        .iframe-container {
            width: 100%;
            height: auto;
            border-radius: 15px;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }

        .iframe-container iframe {
            width: 393px;
            height: 852px;
            border: none;
        }

        .loading {
            color: #7F8C8D;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .loading i {
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from {
                transform: rotate(0deg);
            }

            to {
                transform: rotate(360deg);
            }
        }

        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .feature {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 15px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .feature i {
            font-size: 1.5rem;
            color: #FFF3C4;
            margin-bottom: 8px;
        }

        .feature h4 {
            color: white;
            font-size: 0.9rem;
            margin-bottom: 4px;
        }

        .feature p {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.8rem;
        }

        @media (max-width: 1400px) {
            .prototype-grid {
                grid-template-columns: repeat(3, 1fr);
            }
        }

        @media (max-width: 1024px) {
            .prototype-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 15px;
            }
        }

        @media (max-width: 768px) {
            .prototype-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .app-names {
                flex-direction: column;
                gap: 20px;
            }

            .header h1 {
                font-size: 2rem;
            }

        }

        @media (max-width: 480px) {
            body {
                padding: 10px;
            }

        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-paw"></i> 萌宠记原型展示</h1>
            <p>基于MVP功能规划的高保真移动端原型设计</p>

            <div class="app-info">
                <div class="app-names">
                    <div class="app-name">
                        <h3>萌宠记</h3>
                        <p>中文品牌名</p>
                    </div>
                    <div class="app-name">
                        <h3>PetMoments</h3>
                        <p>英文品牌名</p>
                    </div>
                </div>

                <div class="features">
                    <div class="feature">
                        <i class="fas fa-camera"></i>
                        <h4>成长记录</h4>
                        <p>宠物档案与相册</p>
                    </div>
                    <div class="feature">
                        <i class="fas fa-users"></i>
                        <h4>社交互动</h4>
                        <p>分享交流献花</p>
                    </div>
                    <div class="feature">
                        <i class="fas fa-hashtag"></i>
                        <h4>话题广场</h4>
                        <p>品种圈同城圈</p>
                    </div>
                    <div class="feature">
                        <i class="fas fa-trophy"></i>
                        <h4>成长体系</h4>
                        <p>等级徽章里程碑</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="prototype-grid">
            <div class="prototype-item">
                <div class="prototype-header">
                    <div class="prototype-icon">
                        <i class="fas fa-home"></i>
                    </div>
                    <div class="prototype-title">
                        <h3>主页 Feed</h3>
                        <p>推荐/关注/同城内容流</p>
                    </div>
                </div>
                <div class="iframe-container">
                    <iframe src="home.html"></iframe>
                </div>
            </div>

            <div class="prototype-item">
                <div class="prototype-header">
                    <div class="prototype-icon">
                        <i class="fas fa-hashtag"></i>
                    </div>
                    <div class="prototype-title">
                        <h3>话题广场</h3>
                        <p>热门话题与投稿入口</p>
                    </div>
                </div>
                <div class="iframe-container">
                    <iframe src="topics.html"></iframe>
                </div>
            </div>

            <div class="prototype-item">
                <div class="prototype-header">
                    <div class="prototype-icon">
                        <i class="fas fa-plus-circle"></i>
                    </div>
                    <div class="prototype-title">
                        <h3>创作发布</h3>
                        <p>图文视频发布与编辑</p>
                    </div>
                </div>
                <div class="iframe-container">
                    <iframe src="create.html"></iframe>
                </div>
            </div>

            <div class="prototype-item">
                <div class="prototype-header">
                    <div class="prototype-icon">
                        <i class="fas fa-bell"></i>
                    </div>
                    <div class="prototype-title">
                        <h3>消息中心</h3>
                        <p>互动通知与系统消息</p>
                    </div>
                </div>
                <div class="iframe-container">
                    <iframe src="messages.html"></iframe>
                </div>
            </div>

            <div class="prototype-item">
                <div class="prototype-header">
                    <div class="prototype-icon">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="prototype-title">
                        <h3>个人中心</h3>
                        <p>用户资料与宠物管理</p>
                    </div>
                </div>
                <div class="iframe-container">
                    <iframe src="profile.html"></iframe>
                </div>
            </div>

            <div class="prototype-item">
                <div class="prototype-header">
                    <div class="prototype-icon">
                        <i class="fas fa-paw"></i>
                    </div>
                    <div class="prototype-title">
                        <h3>宠物档案</h3>
                        <p>宠物详情与成长记录</p>
                    </div>
                </div>
                <div class="iframe-container">
                    <iframe src="pet-profile.html"></iframe>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 简化的页面加载逻辑
        document.addEventListener('DOMContentLoaded', function () {
            // 添加额外的原型页面
            setTimeout(() => {
                addExtraPrototype('内容详情', 'post-detail.html', 'fas fa-file-alt');
                addExtraPrototype('添加宠物', 'add-pet.html', 'fas fa-plus');
                addExtraPrototype('搜索结果', 'search-results.html', 'fas fa-search');
                addExtraPrototype('设置页面', 'settings.html', 'fas fa-cog');
                addExtraPrototype('消息中心', 'messages.html', 'fas fa-bell');
                addExtraPrototype('数据状态', 'empty-states.html', 'fas fa-exclamation-circle');
            }, 500);
        });

        function addExtraPrototype(title, src, iconClass) {
            const grid = document.querySelector('.prototype-grid');
            const extraItem = document.createElement('div');
            extraItem.className = 'prototype-item';

            // 根据不同页面设置描述
            let description = '';
            switch (title) {
                case '内容详情':
                    description = '帖子详情页面与评论互动';
                    break;
                case '添加宠物':
                    description = '宠物信息录入与档案创建';
                    break;
                case '搜索结果':
                    description = '全局搜索与结果展示';
                    break;
                case '设置页面':
                    description = '账号设置与系统配置';
                    break;
                case '消息中心':
                    description = '互动通知与系统消息';
                    break;
                case '数据状态':
                    description = '空状态、加载、错误状态展示';
                    break;
                default:
                    description = '功能页面展示';
            }

            extraItem.innerHTML = `
                <div class="prototype-header">
                    <div class="prototype-icon">
                        <i class="${iconClass}"></i>
                    </div>
                    <div class="prototype-title">
                        <h3>${title}</h3>
                        <p>${description}</p>
                    </div>
                </div>
                <div class="iframe-container">
                    <iframe src="${src}"></iframe>
                </div>
            `;
            grid.appendChild(extraItem);
        }

        // 添加交互效果
        document.addEventListener('DOMContentLoaded', function () {
            document.querySelectorAll('.prototype-item').forEach(item => {
                item.addEventListener('mouseenter', function () {
                    this.style.transform = 'translateY(-5px)';
                });
                item.addEventListener('mouseleave', function () {
                    this.style.transform = 'translateY(0)';
                });
            });
        });
    </script>
</body>

</html>
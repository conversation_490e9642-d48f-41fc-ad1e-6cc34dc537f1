<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>萌宠记 - 搜索结果</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
    <style>
        /* 搜索结果页面特定样式 */
        .search-header {
            display: flex;
            align-items: center;
            padding: 12px 16px;
            background: var(--background);
            border-bottom: 1px solid var(--border-color);
        }

        .back-btn {
            background: none;
            border: none;
            font-size: 18px;
            color: var(--text-primary);
            cursor: pointer;
            margin-right: 12px;
        }

        .search-input-container {
            flex: 1;
            position: relative;
        }

        .search-input {
            width: 100%;
            padding: 8px 40px 8px 16px;
            border: 1px solid var(--border-color);
            border-radius: 20px;
            font-size: 14px;
            background: var(--background-secondary);
            outline: none;
        }

        .search-btn {
            position: absolute;
            right: 8px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: var(--primary-color);
            font-size: 14px;
            cursor: pointer;
        }

        .search-tabs {
            display: flex;
            background: var(--background);
            border-bottom: 1px solid var(--border-color);
            overflow-x: auto;
        }

        .search-tab {
            flex-shrink: 0;
            padding: 16px 20px;
            color: var(--text-secondary);
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            position: relative;
            white-space: nowrap;
        }

        .search-tab.active {
            color: var(--primary-color);
            font-weight: 600;
        }

        .search-tab.active::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 30px;
            height: 3px;
            background: var(--primary-color);
            border-radius: 2px;
        }

        .search-results {
            padding: 16px;
        }

        .result-count {
            font-size: 12px;
            color: var(--text-secondary);
            margin-bottom: 16px;
        }

        /* 用户结果样式 */
        .user-result {
            display: flex;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid var(--border-color);
            cursor: pointer;
        }

        .user-result:last-child {
            border-bottom: none;
        }

        .user-result:hover {
            background: var(--background-secondary);
            margin: 0 -16px;
            padding-left: 16px;
            padding-right: 16px;
            border-radius: 12px;
        }

        .user-avatar {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            object-fit: cover;
            margin-right: 12px;
        }

        .user-info {
            flex: 1;
        }

        .user-name {
            font-size: 15px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 2px;
        }

        .user-desc {
            font-size: 12px;
            color: var(--text-secondary);
        }

        .follow-btn {
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 16px;
            padding: 6px 16px;
            font-size: 12px;
            font-weight: 500;
            cursor: pointer;
        }

        .follow-btn.following {
            background: var(--background-secondary);
            color: var(--text-secondary);
        }

        /* 内容结果样式 */
        .content-result {
            margin-bottom: 16px;
            background: var(--background);
            border-radius: 12px;
            overflow: hidden;
            box-shadow: var(--shadow);
        }

        .content-image {
            width: 100%;
            height: 200px;
            object-fit: cover;
        }

        .content-info {
            padding: 12px;
        }

        .content-title {
            font-size: 14px;
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 8px;
            line-height: 1.4;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .content-meta {
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-size: 12px;
            color: var(--text-secondary);
        }

        .content-author {
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .content-stats {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        /* 话题结果样式 */
        .topic-result {
            display: flex;
            align-items: center;
            padding: 16px;
            background: var(--background);
            border-radius: 12px;
            margin-bottom: 12px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .topic-result:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        }

        .topic-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
            margin-right: 12px;
        }

        .topic-info {
            flex: 1;
        }

        .topic-name {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 4px;
        }

        .topic-stats {
            font-size: 12px;
            color: var(--text-secondary);
        }

        .join-btn {
            background: var(--background-secondary);
            color: var(--text-primary);
            border: none;
            border-radius: 16px;
            padding: 8px 16px;
            font-size: 12px;
            font-weight: 500;
            cursor: pointer;
        }

        .join-btn.joined {
            background: var(--primary-color);
            color: white;
        }

        /* 空状态 */
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: var(--text-secondary);
        }

        .empty-state i {
            font-size: 48px;
            margin-bottom: 16px;
            opacity: 0.5;
        }

        .empty-state h3 {
            font-size: 16px;
            font-weight: 500;
            margin-bottom: 8px;
        }

        .empty-state p {
            font-size: 14px;
            line-height: 1.4;
        }

        /* 搜索建议 */
        .search-suggestions {
            padding: 16px;
        }

        .suggestion-title {
            font-size: 14px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 12px;
        }

        .suggestion-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .suggestion-tag {
            background: var(--background-secondary);
            color: var(--text-primary);
            padding: 6px 12px;
            border-radius: 16px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .suggestion-tag:hover {
            background: var(--primary-color);
            color: white;
        }
    </style>
</head>

<body>
    <div class="iphone-container">
        <!-- iOS 状态栏 -->
        <div class="status-bar">
            <div class="status-left">
                <span>9:41</span>
            </div>
            <div class="status-right">
                <i class="fas fa-signal"></i>
                <i class="fas fa-wifi"></i>
                <div class="battery">
                    <div class="battery-fill"></div>
                </div>
            </div>
        </div>

        <!-- 主内容区域 -->
        <div class="main-content">
            <!-- 搜索头部 -->
            <div class="search-header">
                <button class="back-btn" onclick="window.location.href='home.html'">
                    <i class="fas fa-arrow-left"></i>
                </button>
                <div class="search-input-container">
                    <input type="text" class="search-input" placeholder="搜索用户、内容、话题..." value="金毛" id="search-input">
                    <button class="search-btn">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>

            <!-- 搜索分类标签 -->
            <div class="search-tabs">
                <div class="search-tab active" data-tab="all">全部</div>
                <div class="search-tab" data-tab="users">用户</div>
                <div class="search-tab" data-tab="content">内容</div>
                <div class="search-tab" data-tab="topics">话题</div>
                <div class="search-tab" data-tab="pets">宠物</div>
            </div>

            <!-- 搜索结果 -->
            <div class="search-results">
                <div class="result-count">找到 156 个相关结果</div>

                <!-- 用户结果 -->
                <div class="user-result">
                    <img src="https://images.unsplash.com/photo-1494790108755-2616c9c9b8d4?w=96&h=96&fit=crop&crop=face"
                        alt="用户头像" class="user-avatar">
                    <div class="user-info">
                        <div class="user-name">金毛妈妈小雅</div>
                        <div class="user-desc">2只金毛 · 北京 · 128个作品</div>
                    </div>
                    <button class="follow-btn">关注</button>
                </div>

                <div class="user-result">
                    <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=96&h=96&fit=crop&crop=face"
                        alt="用户头像" class="user-avatar">
                    <div class="user-info">
                        <div class="user-name">金毛控阿明</div>
                        <div class="user-desc">1只金毛 · 上海 · 89个作品</div>
                    </div>
                    <button class="follow-btn following">已关注</button>
                </div>

                <!-- 话题结果 -->
                <div class="topic-result">
                    <div class="topic-icon">
                        <i class="fas fa-hashtag"></i>
                    </div>
                    <div class="topic-info">
                        <div class="topic-name">#金毛日常</div>
                        <div class="topic-stats">2.3万参与 · 今日新增156</div>
                    </div>
                    <button class="join-btn">参与</button>
                </div>

                <div class="topic-result">
                    <div class="topic-icon">
                        <i class="fas fa-paw"></i>
                    </div>
                    <div class="topic-info">
                        <div class="topic-name">#金毛训练</div>
                        <div class="topic-stats">8.9k参与 · 今日新增43</div>
                    </div>
                    <button class="join-btn joined">已参与</button>
                </div>

                <!-- 内容结果 -->
                <div class="content-result">
                    <img src="https://images.unsplash.com/photo-1552053831-71594a27632d?w=400&h=200&fit=crop" alt="内容图片"
                        class="content-image">
                    <div class="content-info">
                        <div class="content-title">我家金毛球球今天学会了新技能！坐下握手都会了，太聪明了</div>
                        <div class="content-meta">
                            <div class="content-author">
                                <img src="https://images.unsplash.com/photo-1494790108755-2616c9c9b8d4?w=20&h=20&fit=crop&crop=face"
                                    alt="作者头像" class="avatar avatar-xs">
                                <span>萌宠小雅</span>
                            </div>
                            <div class="content-stats">
                                <span><i class="fas fa-heart"></i> 128</span>
                                <span><i class="fas fa-comment"></i> 23</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="content-result">
                    <img src="https://images.unsplash.com/photo-1583337130417-3346a1be7dee?w=400&h=200&fit=crop"
                        alt="内容图片" class="content-image">
                    <div class="content-info">
                        <div class="content-title">金毛的换毛期到了，每天梳毛都能梳出一只小狗的毛量</div>
                        <div class="content-meta">
                            <div class="content-author">
                                <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=20&h=20&fit=crop&crop=face"
                                    alt="作者头像" class="avatar avatar-xs">
                                <span>金毛控阿明</span>
                            </div>
                            <div class="content-stats">
                                <span><i class="fas fa-heart"></i> 89</span>
                                <span><i class="fas fa-comment"></i> 15</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 搜索建议 -->
            <div class="search-suggestions">
                <div class="suggestion-title">相关搜索</div>
                <div class="suggestion-tags">
                    <div class="suggestion-tag">金毛训练</div>
                    <div class="suggestion-tag">金毛喂养</div>
                    <div class="suggestion-tag">金毛美容</div>
                    <div class="suggestion-tag">金毛健康</div>
                    <div class="suggestion-tag">金毛幼犬</div>
                </div>
            </div>
        </div>

        <!-- 底部导航栏 -->
        <div class="tab-bar">
            <a href="home.html" class="tab-item">
                <i class="fas fa-home"></i>
                <span>主页</span>
            </a>
            <a href="topics.html" class="tab-item active">
                <i class="fas fa-hashtag"></i>
                <span>话题</span>
            </a>
            <a href="create.html" class="tab-item">
                <i class="fas fa-plus-circle"></i>
                <span>创作</span>
            </a>
            <a href="messages.html" class="tab-item">
                <i class="fas fa-bell"></i>
                <span>消息</span>
            </a>
            <a href="profile.html" class="tab-item">
                <i class="fas fa-user"></i>
                <span>我的</span>
            </a>
        </div>
    </div>

    <script>
        // 搜索标签切换
        document.querySelectorAll('.search-tab').forEach(tab => {
            tab.addEventListener('click', function () {
                document.querySelectorAll('.search-tab').forEach(t => t.classList.remove('active'));
                this.classList.add('active');

                const tabType = this.dataset.tab;
                filterResults(tabType);
            });
        });

        function filterResults(type) {
            // 这里可以根据不同标签过滤结果
            console.log('切换到:', type);
        }

        // 关注按钮
        document.querySelectorAll('.follow-btn').forEach(btn => {
            btn.addEventListener('click', function (e) {
                e.stopPropagation();
                if (this.classList.contains('following')) {
                    this.classList.remove('following');
                    this.textContent = '关注';
                    this.style.background = 'var(--primary-color)';
                    this.style.color = 'white';
                } else {
                    this.classList.add('following');
                    this.textContent = '已关注';
                    this.style.background = 'var(--background-secondary)';
                    this.style.color = 'var(--text-secondary)';
                }
            });
        });

        // 参与话题按钮
        document.querySelectorAll('.join-btn').forEach(btn => {
            btn.addEventListener('click', function (e) {
                e.stopPropagation();
                if (this.classList.contains('joined')) {
                    this.classList.remove('joined');
                    this.textContent = '参与';
                    this.style.background = 'var(--background-secondary)';
                    this.style.color = 'var(--text-primary)';
                } else {
                    this.classList.add('joined');
                    this.textContent = '已参与';
                    this.style.background = 'var(--primary-color)';
                    this.style.color = 'white';
                }
            });
        });

        // 搜索建议标签点击
        document.querySelectorAll('.suggestion-tag').forEach(tag => {
            tag.addEventListener('click', function () {
                document.getElementById('search-input').value = this.textContent;
                // 触发搜索
                console.log('搜索:', this.textContent);
            });
        });

        // 搜索按钮
        document.querySelector('.search-btn').addEventListener('click', function () {
            const query = document.getElementById('search-input').value.trim();
            if (query) {
                console.log('搜索:', query);
                // 这里应该执行搜索逻辑
            }
        });

        // 回车搜索
        document.getElementById('search-input').addEventListener('keypress', function (e) {
            if (e.key === 'Enter') {
                const query = this.value.trim();
                if (query) {
                    console.log('搜索:', query);
                    // 这里应该执行搜索逻辑
                }
            }
        });

        // 用户结果点击
        document.querySelectorAll('.user-result').forEach(result => {
            result.addEventListener('click', function () {
                console.log('查看用户详情');
                // 跳转到用户详情页
            });
        });

        // 话题结果点击
        document.querySelectorAll('.topic-result').forEach(result => {
            result.addEventListener('click', function () {
                console.log('查看话题详情');
                // 跳转到话题详情页
            });
        });

        // 内容结果点击
        document.querySelectorAll('.content-result').forEach(result => {
            result.addEventListener('click', function () {
                console.log('查看内容详情');
                // 跳转到内容详情页
                window.location.href = 'post-detail.html';
            });
        });
    </script>
</body>

</html>
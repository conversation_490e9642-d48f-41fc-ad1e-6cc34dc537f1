/* 萌宠记 PetMoments - 公共样式 */
@import url("https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap");

/* iPhone 15 Pro 尺寸和基础样式 */
:root {
  --iphone-width: 390px;
  --iphone-height: 844px;
  --status-bar-height: 44px;
  --tab-bar-height: 80px;
  --content-height: calc(
    var(--iphone-height) - var(--status-bar-height) - var(--tab-bar-height)
  );

  /* 品牌色彩 - 优雅紫色系 (参考cosplay_app) */
  --primary: #8a2be2;
  --primary-light: #9d4edd;
  --secondary: #ffd166;
  --dark: #2d3748;
  --light: #f7fafc;
  --danger: #e53e3e;
  --success: #38a169;
  --warning: #dd6b20;
  --gray-100: #f7fafc;
  --gray-200: #edf2f7;
  --gray-300: #e2e8f0;
  --gray-400: #cbd5e0;
  --gray-500: #a0aec0;
  --gray-600: #718096;
  --gray-700: #4a5568;
  --gray-800: #2d3748;
  --gray-900: #1a202c;

  /* 兼容性别名 */
  --primary-color: var(--primary);
  --text-primary: var(--gray-800);
  --text-secondary: var(--gray-600);
  --background: #ffffff;
  --background-secondary: var(--gray-100);
  --border-color: var(--gray-300);
  --shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  --shadow-hover: 0 6px 15px rgba(138, 43, 226, 0.4);
  --shadow-active: 0 1px 4px rgba(0, 0, 0, 0.1);
  --border-radius: 12px;
  --border-radius-sm: 8px;
  --border-radius-lg: 16px;
  --transition: all 0.2s ease;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Noto Sans SC", -apple-system, BlinkMacSystemFont, "Segoe UI",
    Roboto, sans-serif;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  margin: 0;
  padding: 0;
  background-color: #f5f5f5;
  color: var(--gray-800);
}

/* iPhone 容器样式 */
.iphone-container {
  width: var(--iphone-width);
  height: var(--iphone-height);
  background: var(--background);
  border-radius: 40px;
  overflow: hidden;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  position: relative;
  border: 10px solid #000;
  margin: 20px auto;
  display: block;
  box-sizing: border-box;
}

/* iOS 状态栏 */
.status-bar {
  height: var(--status-bar-height);
  background-color: #000;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  color: white;
  font-size: 12px;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  width: 100%;
}

.status-bar-notch {
  position: absolute;
  width: 150px;
  height: 30px;
  background-color: #000;
  border-radius: 0 0 20px 20px;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
}

.status-left {
  display: flex;
  align-items: center;
  gap: 4px;
}

.status-right {
  display: flex;
  align-items: center;
  gap: 4px;
}

.battery {
  width: 24px;
  height: 12px;
  border: 1px solid var(--text-primary);
  border-radius: 2px;
  position: relative;
}

.battery::after {
  content: "";
  position: absolute;
  right: -3px;
  top: 3px;
  width: 2px;
  height: 6px;
  background: var(--text-primary);
  border-radius: 0 1px 1px 0;
}

.battery-fill {
  height: 100%;
  background: #34c759;
  border-radius: 1px;
  width: 80%;
}

/* 主内容区域 */
.main-content {
  height: calc(100% - 44px - 80px);
  overflow-y: auto;
  background-color: var(--gray-100);
  padding-bottom: 80px;
  padding-top: 44px;
  position: relative;
}

/* 统一按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 12px 24px;
  border: none;
  border-radius: var(--border-radius);
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
  text-decoration: none;
  gap: 8px;
}

.btn-primary {
  background-color: var(--primary);
  color: white;
  box-shadow: 0 4px 12px rgba(138, 43, 226, 0.3);
}

.btn-primary:hover {
  background-color: var(--primary-light);
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(138, 43, 226, 0.4);
}

.btn-secondary {
  background: var(--background-secondary);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

.btn-secondary:hover {
  background: var(--border-color);
  transform: translateY(-1px);
}

.btn-sm {
  padding: 8px 16px;
  font-size: 12px;
  border-radius: var(--border-radius-sm);
}

.btn-lg {
  padding: 16px 32px;
  font-size: 16px;
  border-radius: var(--border-radius-lg);
}

/* 统一卡片样式 */
.card {
  background: #fff;
  border-radius: 16px;
  overflow: hidden;
  margin: 10px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  transition: var(--transition);
}

.card:hover {
  box-shadow: var(--shadow-hover);
  transform: translateY(-2px);
}

.glass-card {
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  overflow: hidden;
  margin: 10px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.card-sm {
  border-radius: var(--border-radius-sm);
}

.card-lg {
  border-radius: var(--border-radius-lg);
}

/* 统一输入框样式 */
.input,
.form-control {
  width: 100%;
  padding: 14px 16px;
  border: 1px solid var(--gray-300);
  border-radius: 12px;
  font-size: 16px;
  background: white;
  color: var(--gray-800);
  outline: none;
  transition: var(--transition);
  max-width: 100%;
  box-sizing: border-box;
}

.input:focus,
.form-control:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(138, 43, 226, 0.2);
}

.input-sm {
  padding: 8px 12px;
  font-size: 12px;
  border-radius: var(--border-radius-sm);
}

.input-lg {
  padding: 16px 20px;
  font-size: 16px;
  border-radius: var(--border-radius-lg);
}

/* 统一头像样式 */
.avatar {
  border-radius: 50%;
  object-fit: cover;
}

.avatar-xs {
  width: 20px;
  height: 20px;
}

.avatar-sm {
  width: 32px;
  height: 32px;
}

.avatar-md {
  width: 48px;
  height: 48px;
}

.avatar-lg {
  width: 64px;
  height: 64px;
}

.avatar-xl {
  width: 96px;
  height: 96px;
}

/* 底部导航栏 */
.tab-bar,
.bottom-nav {
  height: 80px;
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-top: 1px solid var(--gray-200);
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 0 10px;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 100;
}

.tab-item,
.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--gray-600);
  text-decoration: none;
  font-size: 10px;
  font-weight: 500;
  width: 60px;
  height: 60px;
  transition: color 0.2s;
}

.tab-item.active,
.nav-item.active {
  color: var(--primary);
  position: relative;
}

.tab-item.active::after {
  content: "";
  position: absolute;
  bottom: -2px;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 3px;
  background: var(--primary);
  border-radius: 2px;
}

.tab-item i,
.nav-item i {
  font-size: 22px;
  margin-bottom: 5px;
}

/* 通用组件样式 */
.btn-primary {
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: 20px;
  padding: 12px 24px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
}

.btn-primary:hover {
  background: #ff5252;
  transform: translateY(-1px);
}

.btn-secondary {
  background: var(--background-secondary);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
  border-radius: 20px;
  padding: 8px 16px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.card {
  background: var(--background);
  border-radius: 16px;
  padding: 16px;
  margin-bottom: 12px;
  box-shadow: var(--shadow);
  border: 1px solid var(--border-color);
}

.avatar {
  border-radius: 50%;
  object-fit: cover;
}

.avatar-sm {
  width: 32px;
  height: 32px;
}

.avatar-md {
  width: 48px;
  height: 48px;
}

.avatar-lg {
  width: 80px;
  height: 80px;
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.slide-up {
  animation: slideUp 0.4s ease-in-out;
}

/* 响应式调整 */
@media (max-width: 480px) {
  .iphone-container {
    width: 100%;
    max-width: var(--iphone-width);
    height: 100vh;
    max-height: var(--iphone-height);
    border-radius: 20px;
    border-width: 4px;
  }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 4px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 2px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-secondary);
}

/* 高级互动效果 */
.hover-lift {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.hover-scale {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-scale:hover {
  transform: scale(1.02);
}

.hover-glow {
  transition: all 0.3s ease;
  position: relative;
}

.hover-glow:hover {
  box-shadow: 0 0 20px rgba(255, 183, 77, 0.4);
}

.ripple-effect {
  position: relative;
  overflow: hidden;
}

.ripple-effect::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.ripple-effect:active::before {
  width: 300px;
  height: 300px;
}

/* 渐变动画 */
.gradient-animate {
  background-size: 200% 200%;
  animation: gradientShift 3s ease infinite;
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* 脉冲效果 */
.pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

/* 返回按钮统一样式 */
.back-btn,
.back-button {
  position: absolute;
  top: 15px;
  left: 15px;
  z-index: 10;
  width: 36px;
  height: 36px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  backdrop-filter: blur(5px);
  transition: all 0.2s;
  border: none;
  cursor: pointer;
  font-size: 18px;
}

.back-btn:hover,
.back-button:hover {
  background: var(--background-secondary);
  transform: scale(1.1);
}

.back-btn:active,
.back-button:active {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 0.3);
}

/* 通用工具类 */
.text-center {
  text-align: center;
}
.text-primary {
  color: var(--primary);
}
.bg-primary {
  background-color: var(--primary);
}
.flex {
  display: flex;
}
.items-center {
  align-items: center;
}
.justify-center {
  justify-content: center;
}
.justify-between {
  justify-content: space-between;
}
.mt-2 {
  margin-top: 0.5rem;
}
.mb-2 {
  margin-bottom: 0.5rem;
}
.mt-4 {
  margin-top: 1rem;
}
.mb-4 {
  margin-bottom: 1rem;
}
.p-4 {
  padding: 1rem;
}
.rounded-full {
  border-radius: 9999px;
}
.font-bold {
  font-weight: 700;
}
.text-sm {
  font-size: 0.875rem;
}
.text-lg {
  font-size: 1.125rem;
}
.text-xl {
  font-size: 1.25rem;
}
.text-2xl {
  font-size: 1.5rem;
}
.w-full {
  width: 100%;
}
.h-full {
  height: 100%;
}
.relative {
  position: relative;
}
.absolute {
  position: absolute;
}
.overflow-hidden {
  overflow: hidden;
}

/* 表单样式 */
.form-group {
  margin-bottom: 20px;
  width: 100%;
  max-width: 100%;
}

.form-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  font-size: 14px;
  color: var(--gray-700);
}

textarea,
select {
  width: 100%;
  max-width: 100%;
  padding: 12px 15px;
  border-radius: 12px;
  border: 1px solid var(--gray-300);
  font-size: 15px;
  color: var(--gray-800);
  background: white;
  box-sizing: border-box;
}

textarea {
  min-height: 100px;
  resize: vertical;
}

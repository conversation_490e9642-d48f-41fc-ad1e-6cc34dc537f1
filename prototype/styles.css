/* 萌宠记 PetMoments - 公共样式 */

/* iPhone 15 Pro 尺寸和基础样式 */
:root {
  --iphone-width: 393px;
  --iphone-height: 852px;
  --status-bar-height: 47px;
  --tab-bar-height: 83px;
  --content-height: calc(
    var(--iphone-height) - var(--status-bar-height) - var(--tab-bar-height)
  );

  /* 品牌色彩 - 活泼黄色系 */
  --primary-color: #ffb74d;
  --secondary-color: #ffcc02;
  --accent-color: #fff3c4;
  --text-primary: #2c3e50;
  --text-secondary: #7f8c8d;
  --background: #ffffff;
  --background-secondary: #f8f9fa;
  --border-color: #e9ecef;
  --shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  --shadow-hover: 0 4px 20px rgba(0, 0, 0, 0.15);
  --shadow-active: 0 1px 4px rgba(0, 0, 0, 0.1);
  --border-radius: 12px;
  --border-radius-sm: 8px;
  --border-radius-lg: 16px;
  --transition: all 0.2s ease;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
}

/* iPhone 容器样式 */
.iphone-container {
  width: var(--iphone-width);
  height: var(--iphone-height);
  background: var(--background);
  border-radius: 40px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  position: relative;
  border: 8px solid #1a1a1a;
}

/* iOS 状态栏 */
.status-bar {
  height: var(--status-bar-height);
  background: var(--background);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
  position: relative;
  z-index: 1000;
}

.status-left {
  display: flex;
  align-items: center;
  gap: 4px;
}

.status-right {
  display: flex;
  align-items: center;
  gap: 4px;
}

.battery {
  width: 24px;
  height: 12px;
  border: 1px solid var(--text-primary);
  border-radius: 2px;
  position: relative;
}

.battery::after {
  content: "";
  position: absolute;
  right: -3px;
  top: 3px;
  width: 2px;
  height: 6px;
  background: var(--text-primary);
  border-radius: 0 1px 1px 0;
}

.battery-fill {
  height: 100%;
  background: #34c759;
  border-radius: 1px;
  width: 80%;
}

/* 主内容区域 */
.main-content {
  height: var(--content-height);
  overflow-y: auto;
  background: var(--background);
  position: relative;
}

/* 统一按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 12px 24px;
  border: none;
  border-radius: var(--border-radius);
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
  text-decoration: none;
  gap: 8px;
}

.btn-primary {
  background: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background: #e55a2b;
  transform: translateY(-1px);
  box-shadow: var(--shadow-hover);
}

.btn-secondary {
  background: var(--background-secondary);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

.btn-secondary:hover {
  background: var(--border-color);
  transform: translateY(-1px);
}

.btn-sm {
  padding: 8px 16px;
  font-size: 12px;
  border-radius: var(--border-radius-sm);
}

.btn-lg {
  padding: 16px 32px;
  font-size: 16px;
  border-radius: var(--border-radius-lg);
}

/* 统一卡片样式 */
.card {
  background: var(--background);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  transition: var(--transition);
}

.card:hover {
  box-shadow: var(--shadow-hover);
  transform: translateY(-2px);
}

.card-sm {
  border-radius: var(--border-radius-sm);
}

.card-lg {
  border-radius: var(--border-radius-lg);
}

/* 统一输入框样式 */
.input {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: 14px;
  background: var(--background);
  color: var(--text-primary);
  outline: none;
  transition: var(--transition);
}

.input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(255, 112, 67, 0.1);
}

.input-sm {
  padding: 8px 12px;
  font-size: 12px;
  border-radius: var(--border-radius-sm);
}

.input-lg {
  padding: 16px 20px;
  font-size: 16px;
  border-radius: var(--border-radius-lg);
}

/* 统一头像样式 */
.avatar {
  border-radius: 50%;
  object-fit: cover;
}

.avatar-xs {
  width: 20px;
  height: 20px;
}

.avatar-sm {
  width: 32px;
  height: 32px;
}

.avatar-md {
  width: 48px;
  height: 48px;
}

.avatar-lg {
  width: 64px;
  height: 64px;
}

.avatar-xl {
  width: 96px;
  height: 96px;
}

/* 底部导航栏 */
.tab-bar {
  height: var(--tab-bar-height);
  background: var(--background);
  border-top: 1px solid var(--border-color);
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding-bottom: 20px;
  position: relative;
  z-index: 1000;
}

.tab-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  color: var(--text-secondary);
  text-decoration: none;
  font-size: 10px;
  font-weight: 500;
  transition: color 0.2s;
}

.tab-item.active {
  color: var(--primary-color);
  position: relative;
}

.tab-item.active::after {
  content: "";
  position: absolute;
  bottom: -2px;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 3px;
  background: var(--primary-color);
  border-radius: 2px;
}

.tab-item i {
  font-size: 20px;
}

/* 通用组件样式 */
.btn-primary {
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: 20px;
  padding: 12px 24px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
}

.btn-primary:hover {
  background: #ff5252;
  transform: translateY(-1px);
}

.btn-secondary {
  background: var(--background-secondary);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
  border-radius: 20px;
  padding: 8px 16px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.card {
  background: var(--background);
  border-radius: 16px;
  padding: 16px;
  margin-bottom: 12px;
  box-shadow: var(--shadow);
  border: 1px solid var(--border-color);
}

.avatar {
  border-radius: 50%;
  object-fit: cover;
}

.avatar-sm {
  width: 32px;
  height: 32px;
}

.avatar-md {
  width: 48px;
  height: 48px;
}

.avatar-lg {
  width: 80px;
  height: 80px;
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-out;
}

/* 响应式调整 */
@media (max-width: 480px) {
  .iphone-container {
    width: 100%;
    max-width: var(--iphone-width);
    height: 100vh;
    max-height: var(--iphone-height);
    border-radius: 20px;
    border-width: 4px;
  }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 4px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 2px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-secondary);
}

/* 高级互动效果 */
.hover-lift {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.hover-scale {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-scale:hover {
  transform: scale(1.02);
}

.hover-glow {
  transition: all 0.3s ease;
  position: relative;
}

.hover-glow:hover {
  box-shadow: 0 0 20px rgba(255, 183, 77, 0.4);
}

.ripple-effect {
  position: relative;
  overflow: hidden;
}

.ripple-effect::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.ripple-effect:active::before {
  width: 300px;
  height: 300px;
}

/* 渐变动画 */
.gradient-animate {
  background-size: 200% 200%;
  animation: gradientShift 3s ease infinite;
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* 脉冲效果 */
.pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

/* 返回按钮统一样式 */
.back-btn {
  background: none;
  border: none;
  font-size: 18px;
  color: var(--text-primary);
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  transition: all 0.2s;
}

.back-btn:hover {
  background: var(--background-secondary);
  transform: scale(1.1);
}

.back-btn:active {
  transform: scale(0.95);
}

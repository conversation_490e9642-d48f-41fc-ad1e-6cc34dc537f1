<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>萌宠记 - 宠物档案</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
    <style>
        /* 宠物档案特定样式 */
        .pet-header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 20px 16px;
            position: relative;
            overflow: hidden;
        }

        .pet-header::before {
            content: '';
            position: absolute;
            top: -30%;
            right: -30%;
            width: 150px;
            height: 150px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
        }

        .header-nav {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20px;
            position: relative;
            z-index: 1;
        }

        .back-btn {
            background: none;
            border: none;
            color: white;
            font-size: 18px;
            cursor: pointer;
        }

        .edit-btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: none;
            border-radius: 16px;
            padding: 6px 12px;
            font-size: 12px;
            font-weight: 500;
            cursor: pointer;
        }

        .pet-info {
            display: flex;
            align-items: center;
            gap: 16px;
            margin-bottom: 20px;
            position: relative;
            z-index: 1;
        }

        .pet-avatar {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            border: 4px solid rgba(255, 255, 255, 0.3);
            object-fit: cover;
        }

        .pet-details h1 {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .pet-breed {
            font-size: 14px;
            opacity: 0.9;
            margin-bottom: 8px;
        }

        .pet-age {
            background: rgba(255, 255, 255, 0.2);
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            display: inline-block;
        }

        .flower-stats {
            display: flex;
            justify-content: space-around;
            position: relative;
            z-index: 1;
        }

        .flower-stat {
            text-align: center;
        }

        .flower-number {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .flower-label {
            font-size: 12px;
            opacity: 0.9;
        }

        .pet-content {
            padding: 0 16px;
        }

        .growth-stage {
            background: var(--background);
            border-radius: 20px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: var(--shadow);
            text-align: center;
        }

        .stage-circle {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: conic-gradient(var(--primary-color) 0deg 252deg, var(--border-color) 252deg 360deg);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 16px;
            position: relative;
        }

        .stage-inner {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            background: var(--background);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .stage-icon {
            font-size: 24px;
            color: var(--primary-color);
            margin-bottom: 4px;
        }

        .stage-name {
            font-size: 12px;
            font-weight: 600;
            color: var(--text-primary);
        }

        .stage-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 8px;
        }

        .stage-desc {
            font-size: 14px;
            color: var(--text-secondary);
            line-height: 1.4;
        }

        .milestones {
            background: var(--background);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: var(--shadow);
        }

        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .milestone-item {
            display: flex;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid var(--border-color);
        }

        .milestone-item:last-child {
            border-bottom: none;
        }

        .milestone-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            font-size: 16px;
        }

        .milestone-icon.completed {
            background: var(--primary-color);
            color: white;
        }

        .milestone-icon.pending {
            background: var(--background-secondary);
            color: var(--text-secondary);
        }

        .milestone-content {
            flex: 1;
        }

        .milestone-title {
            font-size: 14px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 2px;
        }

        .milestone-date {
            font-size: 12px;
            color: var(--text-secondary);
        }

        .milestone-badge {
            background: var(--accent-color);
            color: var(--text-primary);
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: 600;
        }

        .photo-gallery {
            background: var(--background);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: var(--shadow);
        }

        .gallery-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 8px;
            margin-bottom: 12px;
        }

        .gallery-item {
            aspect-ratio: 1;
            border-radius: 12px;
            overflow: hidden;
            cursor: pointer;
            transition: transform 0.2s;
        }

        .gallery-item:hover {
            transform: scale(1.05);
        }

        .gallery-item img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .view-all-btn {
            width: 100%;
            background: var(--background-secondary);
            color: var(--text-primary);
            border: none;
            border-radius: 12px;
            padding: 12px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
        }

        .view-all-btn:hover {
            background: var(--primary-color);
            color: white;
        }

        .health-records {
            background: var(--background);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: var(--shadow);
        }

        .health-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 0;
            border-bottom: 1px solid var(--border-color);
        }

        .health-item:last-child {
            border-bottom: none;
        }

        .health-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .health-icon {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: var(--background-secondary);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--primary-color);
            font-size: 14px;
        }

        .health-text {
            font-size: 14px;
            color: var(--text-primary);
            font-weight: 500;
        }

        .health-date {
            font-size: 12px;
            color: var(--text-secondary);
        }

        .flower-btn {
            position: fixed;
            bottom: 100px;
            right: 20px;
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, var(--accent-color), #FFFDE7);
            border: none;
            border-radius: 50%;
            color: var(--text-primary);
            font-size: 20px;
            cursor: pointer;
            box-shadow: 0 4px 16px rgba(255, 243, 196, 0.4);
            transition: all 0.2s;
        }

        .flower-btn:hover {
            transform: scale(1.1);
        }
    </style>
</head>

<body>
    <div class="iphone-container">
        <!-- iOS 状态栏 -->
        <div class="status-bar">
            <div class="status-left">
                <span>9:41</span>
            </div>
            <div class="status-right">
                <i class="fas fa-signal"></i>
                <i class="fas fa-wifi"></i>
                <div class="battery">
                    <div class="battery-fill"></div>
                </div>
            </div>
        </div>

        <!-- 主内容区域 -->
        <div class="main-content">
            <!-- 宠物档案头部 -->
            <div class="pet-header">
                <div class="header-nav">
                    <button class="back-btn" onclick="window.location.href='profile.html'">
                        <i class="fas fa-arrow-left"></i>
                    </button>
                    <button class="edit-btn">编辑</button>
                </div>

                <div class="pet-info">
                    <img src="https://images.unsplash.com/photo-1552053831-71594a27632d?w=200&h=200&fit=crop&crop=face"
                        alt="球球" class="pet-avatar">
                    <div class="pet-details">
                        <h1>球球</h1>
                        <div class="pet-breed">金毛寻回犬 · 雄性</div>
                        <div class="pet-age">2岁3个月</div>
                    </div>
                </div>

                <div class="flower-stats">
                    <div class="flower-stat">
                        <div class="flower-number">156</div>
                        <div class="flower-label">总收花</div>
                    </div>
                    <div class="flower-stat">
                        <div class="flower-number">5</div>
                        <div class="flower-label">今日收花</div>
                    </div>
                    <div class="flower-stat">
                        <div class="flower-number">#8</div>
                        <div class="flower-label">金毛圈排名</div>
                    </div>
                </div>
            </div>

            <div class="pet-content">
                <!-- 成长阶段 -->
                <div class="growth-stage">
                    <div class="stage-circle">
                        <div class="stage-inner">
                            <div class="stage-icon">🐕</div>
                            <div class="stage-name">成年期</div>
                        </div>
                    </div>
                    <div class="stage-title">健康成长中</div>
                    <div class="stage-desc">球球正处于活力满满的成年期，是最适合运动和训练的黄金时期</div>
                </div>

                <!-- 成长里程碑 -->
                <div class="milestones">
                    <h3 class="section-title">
                        <i class="fas fa-flag"></i>
                        成长里程碑
                    </h3>

                    <div class="milestone-item">
                        <div class="milestone-icon completed">
                            <i class="fas fa-home"></i>
                        </div>
                        <div class="milestone-content">
                            <div class="milestone-title">领回家</div>
                            <div class="milestone-date">2022年1月15日</div>
                        </div>
                        <div class="milestone-badge">已完成</div>
                    </div>

                    <div class="milestone-item">
                        <div class="milestone-icon completed">
                            <i class="fas fa-shower"></i>
                        </div>
                        <div class="milestone-content">
                            <div class="milestone-title">第一次洗澡</div>
                            <div class="milestone-date">2022年1月22日</div>
                        </div>
                        <div class="milestone-badge">已完成</div>
                    </div>

                    <div class="milestone-item">
                        <div class="milestone-icon completed">
                            <i class="fas fa-syringe"></i>
                        </div>
                        <div class="milestone-content">
                            <div class="milestone-title">疫苗接种</div>
                            <div class="milestone-date">2022年2月10日</div>
                        </div>
                        <div class="milestone-badge">已完成</div>
                    </div>

                    <div class="milestone-item">
                        <div class="milestone-icon completed">
                            <i class="fas fa-graduation-cap"></i>
                        </div>
                        <div class="milestone-content">
                            <div class="milestone-title">学会坐下</div>
                            <div class="milestone-date">2022年3月5日</div>
                        </div>
                        <div class="milestone-badge">已完成</div>
                    </div>

                    <div class="milestone-item">
                        <div class="milestone-icon pending">
                            <i class="fas fa-birthday-cake"></i>
                        </div>
                        <div class="milestone-content">
                            <div class="milestone-title">3岁生日</div>
                            <div class="milestone-date">预计2025年1月15日</div>
                        </div>
                    </div>
                </div>

                <!-- 相册精选 -->
                <div class="photo-gallery">
                    <h3 class="section-title">
                        <i class="fas fa-camera"></i>
                        相册精选
                    </h3>
                    <div class="gallery-grid">
                        <div class="gallery-item">
                            <img src="https://images.unsplash.com/photo-1552053831-71594a27632d?w=200&h=200&fit=crop"
                                alt="球球照片1">
                        </div>
                        <div class="gallery-item">
                            <img src="https://images.unsplash.com/photo-1583337130417-3346a1be7dee?w=200&h=200&fit=crop"
                                alt="球球照片2">
                        </div>
                        <div class="gallery-item">
                            <img src="https://images.unsplash.com/photo-1574158622682-e40e69881006?w=200&h=200&fit=crop"
                                alt="球球照片3">
                        </div>
                        <div class="gallery-item">
                            <img src="https://images.unsplash.com/photo-1601758228041-f3b2795255f1?w=200&h=200&fit=crop"
                                alt="球球照片4">
                        </div>
                        <div class="gallery-item">
                            <img src="https://images.unsplash.com/photo-1587300003388-59208cc962cb?w=200&h=200&fit=crop"
                                alt="球球照片5">
                        </div>
                        <div class="gallery-item">
                            <img src="https://images.unsplash.com/photo-1518717758536-85ae29035b6d?w=200&h=200&fit=crop"
                                alt="球球照片6">
                        </div>
                    </div>
                    <button class="view-all-btn">查看全部 128 张照片</button>
                </div>

                <!-- 健康记录 -->
                <div class="health-records">
                    <h3 class="section-title">
                        <i class="fas fa-heartbeat"></i>
                        健康记录
                    </h3>

                    <div class="health-item">
                        <div class="health-info">
                            <div class="health-icon">
                                <i class="fas fa-weight"></i>
                            </div>
                            <div class="health-text">体重记录</div>
                        </div>
                        <div class="health-date">32.5kg · 昨天</div>
                    </div>

                    <div class="health-item">
                        <div class="health-info">
                            <div class="health-icon">
                                <i class="fas fa-pills"></i>
                            </div>
                            <div class="health-text">驱虫提醒</div>
                        </div>
                        <div class="health-date">下次：4月15日</div>
                    </div>

                    <div class="health-item">
                        <div class="health-info">
                            <div class="health-icon">
                                <i class="fas fa-cut"></i>
                            </div>
                            <div class="health-text">美容护理</div>
                        </div>
                        <div class="health-date">3月20日</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 献花按钮 -->
        <button class="flower-btn pulse">
            <i class="fas fa-seedling"></i>
        </button>

        <!-- 底部导航栏 -->
        <div class="tab-bar">
            <a href="#" class="tab-item">
                <i class="fas fa-home"></i>
                <span>主页</span>
            </a>
            <a href="#" class="tab-item">
                <i class="fas fa-hashtag"></i>
                <span>话题</span>
            </a>
            <a href="#" class="tab-item">
                <i class="fas fa-plus-circle"></i>
                <span>创作</span>
            </a>
            <a href="#" class="tab-item">
                <i class="fas fa-bell"></i>
                <span>消息</span>
            </a>
            <a href="#" class="tab-item active">
                <i class="fas fa-user"></i>
                <span>我的</span>
            </a>
        </div>
    </div>

    <script>
        // 相册图片点击效果
        document.querySelectorAll('.gallery-item').forEach(item => {
            item.addEventListener('click', function () {
                this.style.transform = 'scale(1.1)';
                setTimeout(() => {
                    this.style.transform = 'scale(1)';
                }, 200);
            });
        });

        // 献花按钮点击效果
        document.querySelector('.flower-btn').addEventListener('click', function () {
            this.style.transform = 'scale(0.9)';
            this.style.boxShadow = '0 2px 8px rgba(255, 230, 109, 0.6)';

            // 创建花朵动画效果
            const flower = document.createElement('div');
            flower.innerHTML = '🌸';
            flower.style.position = 'absolute';
            flower.style.right = '30px';
            flower.style.bottom = '160px';
            flower.style.fontSize = '20px';
            flower.style.pointerEvents = 'none';
            flower.style.animation = 'floatUp 1s ease-out forwards';
            document.body.appendChild(flower);

            setTimeout(() => {
                this.style.transform = 'scale(1)';
                this.style.boxShadow = '0 4px 16px rgba(255, 230, 109, 0.4)';
                flower.remove();
            }, 300);
        });

        // 添加花朵上浮动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes floatUp {
                0% {
                    opacity: 1;
                    transform: translateY(0) scale(1);
                }
                100% {
                    opacity: 0;
                    transform: translateY(-50px) scale(1.5);
                }
            }
        `;
        document.head.appendChild(style);

        // 里程碑项点击效果
        document.querySelectorAll('.milestone-item').forEach(item => {
            item.addEventListener('click', function () {
                this.style.backgroundColor = 'var(--background-secondary)';
                setTimeout(() => {
                    this.style.backgroundColor = '';
                }, 200);
            });
        });

        // 健康记录项点击效果
        document.querySelectorAll('.health-item').forEach(item => {
            item.addEventListener('click', function () {
                this.style.backgroundColor = 'var(--background-secondary)';
                setTimeout(() => {
                    this.style.backgroundColor = '';
                }, 200);
            });
        });

        // 返回按钮
        document.querySelector('.back-btn').addEventListener('click', function () {
            this.style.transform = 'scale(0.9)';
            setTimeout(() => {
                this.style.transform = 'scale(1)';
            }, 150);
        });

        // 编辑按钮
        document.querySelector('.edit-btn').addEventListener('click', function () {
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = 'scale(1)';
            }, 150);
        });
    </script>
</body>

</html>
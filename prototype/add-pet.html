<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>萌宠记 - 添加宠物</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
    <style>
        /* 添加宠物页面特定样式 */
        .add-pet-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 16px;
            background: var(--background);
            border-bottom: 1px solid var(--border-color);
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .back-btn {
            background: none;
            border: none;
            font-size: 18px;
            color: var(--text-primary);
            cursor: pointer;
        }

        .header-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
        }

        .save-btn {
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 16px;
            padding: 8px 20px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
        }

        .save-btn:disabled {
            background: var(--text-secondary);
            cursor: not-allowed;
        }

        .form-container {
            padding: 20px 16px;
            background: var(--background);
        }

        .avatar-section {
            text-align: center;
            margin-bottom: 32px;
        }

        .avatar-upload {
            position: relative;
            display: inline-block;
            margin-bottom: 12px;
        }

        .avatar-preview {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: var(--background-secondary);
            border: 3px dashed var(--border-color);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s;
        }

        .avatar-preview:hover {
            border-color: var(--primary-color);
            background: rgba(255, 112, 67, 0.05);
        }

        .avatar-preview img {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            object-fit: cover;
        }

        .avatar-placeholder {
            color: var(--text-secondary);
            text-align: center;
        }

        .avatar-placeholder i {
            font-size: 32px;
            margin-bottom: 8px;
            display: block;
        }

        .avatar-tip {
            font-size: 12px;
            color: var(--text-secondary);
        }

        .form-group {
            margin-bottom: 24px;
        }

        .form-label {
            display: block;
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 8px;
        }

        .form-input {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid var(--border-color);
            border-radius: 12px;
            font-size: 16px;
            background: var(--background);
            color: var(--text-primary);
            outline: none;
            transition: border-color 0.2s;
        }

        .form-input:focus {
            border-color: var(--primary-color);
        }

        .form-select {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid var(--border-color);
            border-radius: 12px;
            font-size: 16px;
            background: var(--background);
            color: var(--text-primary);
            outline: none;
            cursor: pointer;
        }

        .date-input {
            display: flex;
            gap: 8px;
        }

        .date-input select {
            flex: 1;
            padding: 12px 16px;
            border: 1px solid var(--border-color);
            border-radius: 12px;
            font-size: 16px;
            background: var(--background);
            color: var(--text-primary);
            outline: none;
        }

        .gender-options {
            display: flex;
            gap: 12px;
        }

        .gender-option {
            flex: 1;
            padding: 12px;
            border: 2px solid var(--border-color);
            border-radius: 12px;
            text-align: center;
            cursor: pointer;
            transition: all 0.2s;
            background: var(--background);
        }

        .gender-option.selected {
            border-color: var(--primary-color);
            background: rgba(255, 112, 67, 0.1);
            color: var(--primary-color);
        }

        .gender-icon {
            font-size: 24px;
            margin-bottom: 4px;
        }

        .gender-text {
            font-size: 14px;
            font-weight: 500;
        }

        .personality-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .personality-tag {
            padding: 8px 16px;
            border: 1px solid var(--border-color);
            border-radius: 20px;
            font-size: 14px;
            background: var(--background);
            color: var(--text-primary);
            cursor: pointer;
            transition: all 0.2s;
        }

        .personality-tag.selected {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .form-tip {
            font-size: 12px;
            color: var(--text-secondary);
            margin-top: 4px;
            line-height: 1.4;
        }

        .milestone-section {
            background: var(--background-secondary);
            border-radius: 16px;
            padding: 16px;
            margin-top: 24px;
        }

        .milestone-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .milestone-options {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 8px;
        }

        .milestone-option {
            padding: 8px 12px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            font-size: 12px;
            background: var(--background);
            color: var(--text-primary);
            cursor: pointer;
            transition: all 0.2s;
            text-align: center;
        }

        .milestone-option.selected {
            background: var(--accent-color);
            border-color: var(--primary-color);
            color: var(--text-primary);
        }
    </style>
</head>

<body>
    <div class="iphone-container">
        <!-- iOS 状态栏 -->
        <div class="status-bar">
            <div class="status-left">
                <span>9:41</span>
            </div>
            <div class="status-right">
                <i class="fas fa-signal"></i>
                <i class="fas fa-wifi"></i>
                <div class="battery">
                    <div class="battery-fill"></div>
                </div>
            </div>
        </div>

        <!-- 主内容区域 -->
        <div class="main-content">
            <!-- 添加宠物页面头部 -->
            <div class="add-pet-header">
                <div class="header-left">
                    <button class="back-btn" onclick="window.location.href='profile.html'">
                        <i class="fas fa-arrow-left"></i>
                    </button>
                    <h1 class="header-title">添加宠物</h1>
                </div>
                <button class="save-btn" id="save-btn" disabled>保存</button>
            </div>

            <div class="form-container">
                <!-- 头像上传 -->
                <div class="avatar-section">
                    <div class="avatar-upload">
                        <div class="avatar-preview" onclick="document.getElementById('avatar-input').click()">
                            <div class="avatar-placeholder">
                                <i class="fas fa-camera"></i>
                                <div>添加照片</div>
                            </div>
                        </div>
                        <input type="file" id="avatar-input" accept="image/*" style="display: none;">
                    </div>
                    <div class="avatar-tip">为你的毛孩子选择一张可爱的照片</div>
                </div>

                <!-- 基本信息 -->
                <div class="form-group">
                    <label class="form-label">宠物昵称 *</label>
                    <input type="text" class="form-input" id="pet-name" placeholder="给你的毛孩子起个名字" maxlength="20">
                    <div class="form-tip">昵称将显示在宠物档案和动态中</div>
                </div>

                <div class="form-group">
                    <label class="form-label">品种</label>
                    <select class="form-select" id="pet-breed">
                        <option value="">请选择品种</option>
                        <option value="golden-retriever">金毛寻回犬</option>
                        <option value="labrador">拉布拉多</option>
                        <option value="husky">哈士奇</option>
                        <option value="pomeranian">博美</option>
                        <option value="shiba">柴犬</option>
                        <option value="british-shorthair">英国短毛猫</option>
                        <option value="persian">波斯猫</option>
                        <option value="ragdoll">布偶猫</option>
                        <option value="maine-coon">缅因猫</option>
                        <option value="other">其他</option>
                    </select>
                </div>

                <div class="form-group">
                    <label class="form-label">生日</label>
                    <div class="date-input">
                        <select id="birth-year">
                            <option value="">年份</option>
                        </select>
                        <select id="birth-month">
                            <option value="">月份</option>
                        </select>
                        <select id="birth-day">
                            <option value="">日期</option>
                        </select>
                    </div>
                    <div class="form-tip">生日信息将用于计算年龄和成长阶段</div>
                </div>

                <div class="form-group">
                    <label class="form-label">性别</label>
                    <div class="gender-options">
                        <div class="gender-option" data-gender="male">
                            <div class="gender-icon">♂</div>
                            <div class="gender-text">男孩</div>
                        </div>
                        <div class="gender-option" data-gender="female">
                            <div class="gender-icon">♀</div>
                            <div class="gender-text">女孩</div>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">性格标签</label>
                    <div class="personality-tags">
                        <div class="personality-tag" data-tag="活泼">活泼</div>
                        <div class="personality-tag" data-tag="温顺">温顺</div>
                        <div class="personality-tag" data-tag="聪明">聪明</div>
                        <div class="personality-tag" data-tag="粘人">粘人</div>
                        <div class="personality-tag" data-tag="独立">独立</div>
                        <div class="personality-tag" data-tag="胆小">胆小</div>
                        <div class="personality-tag" data-tag="勇敢">勇敢</div>
                        <div class="personality-tag" data-tag="贪吃">贪吃</div>
                    </div>
                    <div class="form-tip">选择最符合你家毛孩子的性格特点（可多选）</div>
                </div>

                <!-- 里程碑预设 -->
                <div class="milestone-section">
                    <div class="milestone-title">
                        <i class="fas fa-flag"></i>
                        已达成的里程碑
                    </div>
                    <div class="milestone-options">
                        <div class="milestone-option" data-milestone="home">领回家</div>
                        <div class="milestone-option" data-milestone="name">起名字</div>
                        <div class="milestone-option" data-milestone="bath">第一次洗澡</div>
                        <div class="milestone-option" data-milestone="vaccine">疫苗接种</div>
                        <div class="milestone-option" data-milestone="walk">第一次遛弯</div>
                        <div class="milestone-option" data-milestone="friend">认识新朋友</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部导航栏 -->
        <div class="tab-bar">
            <a href="home.html" class="tab-item">
                <i class="fas fa-home"></i>
                <span>主页</span>
            </a>
            <a href="topics.html" class="tab-item">
                <i class="fas fa-hashtag"></i>
                <span>话题</span>
            </a>
            <a href="create.html" class="tab-item">
                <i class="fas fa-plus-circle"></i>
                <span>创作</span>
            </a>
            <a href="messages.html" class="tab-item">
                <i class="fas fa-bell"></i>
                <span>消息</span>
            </a>
            <a href="profile.html" class="tab-item active">
                <i class="fas fa-user"></i>
                <span>我的</span>
            </a>
        </div>
    </div>

    <script>
        // 初始化年份选项
        const currentYear = new Date().getFullYear();
        const yearSelect = document.getElementById('birth-year');
        for (let year = currentYear; year >= currentYear - 20; year--) {
            const option = document.createElement('option');
            option.value = year;
            option.textContent = year + '年';
            yearSelect.appendChild(option);
        }

        // 初始化月份选项
        const monthSelect = document.getElementById('birth-month');
        for (let month = 1; month <= 12; month++) {
            const option = document.createElement('option');
            option.value = month;
            option.textContent = month + '月';
            monthSelect.appendChild(option);
        }

        // 初始化日期选项
        const daySelect = document.getElementById('birth-day');
        for (let day = 1; day <= 31; day++) {
            const option = document.createElement('option');
            option.value = day;
            option.textContent = day + '日';
            daySelect.appendChild(option);
        }

        // 性别选择
        document.querySelectorAll('.gender-option').forEach(option => {
            option.addEventListener('click', function () {
                document.querySelectorAll('.gender-option').forEach(opt => opt.classList.remove('selected'));
                this.classList.add('selected');
                checkFormValid();
            });
        });

        // 性格标签选择
        document.querySelectorAll('.personality-tag').forEach(tag => {
            tag.addEventListener('click', function () {
                this.classList.toggle('selected');
            });
        });

        // 里程碑选择
        document.querySelectorAll('.milestone-option').forEach(milestone => {
            milestone.addEventListener('click', function () {
                this.classList.toggle('selected');
            });
        });

        // 头像上传
        document.getElementById('avatar-input').addEventListener('change', function (e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function (e) {
                    const preview = document.querySelector('.avatar-preview');
                    preview.innerHTML = `<img src="${e.target.result}" alt="宠物头像">`;
                };
                reader.readAsDataURL(file);
                checkFormValid();
            }
        });

        // 表单验证
        function checkFormValid() {
            const petName = document.getElementById('pet-name').value.trim();
            const saveBtn = document.getElementById('save-btn');

            if (petName.length > 0) {
                saveBtn.disabled = false;
                saveBtn.style.background = 'var(--primary-color)';
            } else {
                saveBtn.disabled = true;
                saveBtn.style.background = 'var(--text-secondary)';
            }
        }

        // 监听昵称输入
        document.getElementById('pet-name').addEventListener('input', checkFormValid);

        // 保存按钮点击
        document.getElementById('save-btn').addEventListener('click', function () {
            if (!this.disabled) {
                // 收集表单数据
                const formData = {
                    name: document.getElementById('pet-name').value,
                    breed: document.getElementById('pet-breed').value,
                    birthYear: document.getElementById('birth-year').value,
                    birthMonth: document.getElementById('birth-month').value,
                    birthDay: document.getElementById('birth-day').value,
                    gender: document.querySelector('.gender-option.selected')?.dataset.gender,
                    personality: Array.from(document.querySelectorAll('.personality-tag.selected')).map(tag => tag.dataset.tag),
                    milestones: Array.from(document.querySelectorAll('.milestone-option.selected')).map(milestone => milestone.dataset.milestone)
                };

                console.log('宠物信息:', formData);
                alert('宠物信息保存成功！');
                // 这里应该跳转到宠物档案页面
                window.location.href = 'pet-profile.html';
            }
        });

        // 返回按钮
        document.querySelector('.back-btn').addEventListener('click', function () {
            this.style.transform = 'scale(0.9)';
            setTimeout(() => {
                this.style.transform = 'scale(1)';
            }, 150);
        });
    </script>
</body>

</html>
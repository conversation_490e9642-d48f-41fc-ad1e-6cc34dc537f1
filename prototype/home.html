<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>萌宠记 - 主页</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
    <style>
        /* 主页特定样式 */
        .tab-nav {
            display: flex;
            background: var(--background);
            border-bottom: 1px solid var(--border-color);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .tab-nav-item {
            flex: 1;
            text-align: center;
            padding: 16px 0;
            color: var(--text-secondary);
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            position: relative;
        }

        .tab-nav-item.active {
            color: var(--primary);
            font-weight: 600;
        }

        .filter-btn {
            background: none;
            border: none;
            color: var(--text-secondary);
            font-size: 16px;
            cursor: pointer;
            padding: 8px;
            border-radius: 50%;
            transition: all 0.2s;
        }

        .filter-btn:hover {
            background: var(--background-secondary);
            color: var(--primary);
        }

        .filter-panel {
            background: var(--background);
            border-bottom: 1px solid var(--border-color);
            padding: 16px;
            animation: slideDown 0.3s ease;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .filter-section {
            margin-bottom: 16px;
        }

        .filter-section:last-child {
            margin-bottom: 0;
        }

        .filter-title {
            font-size: 14px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 8px;
        }

        .filter-options {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .filter-option {
            background: var(--background-secondary);
            border: 1px solid var(--border-color);
            border-radius: 16px;
            padding: 6px 12px;
            font-size: 12px;
            color: var(--text-primary);
            cursor: pointer;
            transition: all 0.2s;
        }

        .filter-option.active {
            background: var(--primary);
            color: white;
            border-color: var(--primary);
        }

        .filter-option:hover:not(.active) {
            background: var(--border-color);
        }

        .tab-nav-item.active::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 30px;
            height: 3px;
            background: var(--primary);
            border-radius: 2px;
        }

        .feed-container {
            padding: 0 16px;
            padding-bottom: 20px;
        }

        .post-card {
            background: var(--background);
            border-radius: 16px;
            margin-bottom: 16px;
            overflow: hidden;
            box-shadow: var(--shadow);
        }

        .post-header {
            display: flex;
            align-items: center;
            padding: 16px;
            gap: 12px;
        }

        .user-info {
            flex: 1;
        }

        .username {
            font-size: 15px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 2px;
        }

        .post-time {
            font-size: 12px;
            color: var(--text-secondary);
        }

        .follow-btn {
            background: var(--primary);
            color: white;
            border: none;
            border-radius: 16px;
            padding: 6px 16px;
            font-size: 12px;
            font-weight: 500;
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(138, 43, 226, 0.3);
            transition: all 0.2s;
        }

        .follow-btn:hover {
            background: var(--primary-light);
            transform: translateY(-1px);
            box-shadow: 0 6px 15px rgba(138, 43, 226, 0.4);
        }

        .post-content {
            padding: 0 16px 12px;
        }

        .post-text {
            font-size: 15px;
            line-height: 1.4;
            color: var(--text-primary);
            margin-bottom: 12px;
        }

        .post-tags {
            display: flex;
            gap: 8px;
            margin-bottom: 12px;
        }

        .tag {
            background: var(--background-secondary);
            color: var(--primary);
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .post-image {
            width: 100%;
            border-radius: 12px;
            margin-bottom: 12px;
        }

        .pet-info {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            background: var(--background-secondary);
            border-radius: 12px;
            margin-bottom: 12px;
        }

        .pet-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            object-fit: cover;
        }

        .pet-name {
            font-size: 13px;
            font-weight: 600;
            color: var(--text-primary);
        }

        .pet-breed {
            font-size: 11px;
            color: var(--text-secondary);
        }

        .post-actions {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 16px;
            border-top: 1px solid var(--border-color);
        }

        .action-group {
            display: flex;
            gap: 20px;
        }

        .action-btn {
            display: flex;
            align-items: center;
            gap: 6px;
            background: none;
            border: none;
            color: var(--text-secondary);
            font-size: 13px;
            cursor: pointer;
            transition: color 0.2s;
        }

        .action-btn.liked {
            color: var(--primary);
        }

        .action-btn i {
            font-size: 16px;
        }

        .flower-btn {
            background: linear-gradient(135deg, var(--secondary), #FFFACD);
            color: var(--text-primary);
            border: none;
            border-radius: 20px;
            padding: 8px 16px;
            font-size: 12px;
            font-weight: 600;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 4px;
            transition: all 0.2s;
        }

        .flower-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(255, 209, 102, 0.3);
        }

        .loading-more {
            text-align: center;
            padding: 20px;
            color: var(--text-secondary);
            font-size: 14px;
        }
    </style>
</head>

<body>
    <div class="iphone-container">
        <!-- iOS 状态栏 -->
        <div class="status-bar">
            <div class="status-bar-notch"></div>
            <div class="status-bar-left">
                <span>中国移动</span>
                <i class="fas fa-signal"></i>
            </div>
            <div class="status-bar-center status-bar-time">9:41</div>
            <div class="status-bar-right">
                <i class="fas fa-wifi"></i>
                <i class="fas fa-battery-three-quarters"></i>
            </div>
        </div>

        <!-- 主内容区域 -->
        <div class="main-content">
            <!-- 搜索栏 -->
            <div class="search-bar"
                style="padding: 12px 16px; background: var(--background); border-bottom: 1px solid var(--border-color);">
                <div class="search-input-container" style="position: relative;">
                    <input type="text" placeholder="搜索用户、内容、话题..."
                        style="width: 100%; padding: 8px 40px 8px 16px; border: 1px solid var(--border-color); border-radius: 20px; font-size: 14px; background: var(--background-secondary); outline: none;"
                        onclick="window.location.href='search-results.html'">
                    <i class="fas fa-search"
                        style="position: absolute; right: 12px; top: 50%; transform: translateY(-50%); color: var(--text-secondary); font-size: 14px;"></i>
                </div>
            </div>

            <!-- Tab 导航 -->
            <div class="tab-nav">
                <div class="tab-nav-item active" data-tab="recommend">推荐</div>
                <div class="tab-nav-item" data-tab="following">关注</div>
                <div class="tab-nav-item" data-tab="nearby">同城</div>
                <button class="filter-btn" onclick="toggleFilterPanel()">
                    <i class="fas fa-filter"></i>
                </button>
            </div>

            <!-- 筛选面板 -->
            <div class="filter-panel" id="filter-panel" style="display: none;">
                <div class="filter-section">
                    <div class="filter-title">排序方式</div>
                    <div class="filter-options">
                        <button class="filter-option active" data-sort="latest">最新</button>
                        <button class="filter-option" data-sort="hot">最热</button>
                        <button class="filter-option" data-sort="flowers">献花最多</button>
                    </div>
                </div>
                <div class="filter-section">
                    <div class="filter-title">宠物类型</div>
                    <div class="filter-options">
                        <button class="filter-option active" data-type="all">全部</button>
                        <button class="filter-option" data-type="dog">狗狗</button>
                        <button class="filter-option" data-type="cat">猫咪</button>
                        <button class="filter-option" data-type="other">其他</button>
                    </div>
                </div>
            </div>

            <!-- Feed 内容 -->
            <div class="feed-container">
                <!-- 帖子1 -->
                <div class="post-card fade-in hover-lift">
                    <div class="post-header">
                        <img src="https://images.unsplash.com/photo-1494790108755-2616c9c9b8d4?w=100&h=100&fit=crop&crop=face"
                            alt="用户头像" class="avatar avatar-md">
                        <div class="user-info">
                            <div class="username">萌宠小雅</div>
                            <div class="post-time">2小时前</div>
                        </div>
                        <button class="follow-btn ripple-effect">关注</button>
                    </div>

                    <div class="post-content">
                        <div class="post-text">今天带球球去公园玩，它第一次看到这么多小朋友，兴奋得不得了！看它这个开心的样子，我的心都要化了 🥰</div>

                        <div class="post-tags">
                            <span class="tag">#萌宠日常</span>
                            <span class="tag">#金毛</span>
                            <span class="tag">#公园遛狗</span>
                        </div>

                        <img src="https://images.unsplash.com/photo-1552053831-71594a27632d?w=400&h=300&fit=crop"
                            alt="宠物照片" class="post-image">

                        <div class="pet-info">
                            <img src="https://images.unsplash.com/photo-1552053831-71594a27632d?w=64&h=64&fit=crop&crop=face"
                                alt="宠物头像" class="pet-avatar">
                            <div>
                                <div class="pet-name">球球</div>
                                <div class="pet-breed">金毛寻回犬 · 2岁</div>
                            </div>
                        </div>
                    </div>

                    <div class="post-actions">
                        <div class="action-group">
                            <button class="action-btn liked">
                                <i class="fas fa-heart"></i>
                                <span>128</span>
                            </button>
                            <button class="action-btn">
                                <i class="fas fa-comment"></i>
                                <span>23</span>
                            </button>
                            <button class="action-btn">
                                <i class="fas fa-share"></i>
                                <span>分享</span>
                            </button>
                        </div>
                        <button class="flower-btn">
                            <i class="fas fa-seedling"></i>
                            <span>献花</span>
                        </button>
                    </div>
                </div>

                <!-- 帖子2 -->
                <div class="post-card fade-in hover-lift">
                    <div class="post-header">
                        <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face"
                            alt="用户头像" class="avatar avatar-md">
                        <div class="user-info">
                            <div class="username">猫咪控阿明</div>
                            <div class="post-time">4小时前</div>
                        </div>
                        <button class="follow-btn">关注</button>
                    </div>

                    <div class="post-content">
                        <div class="post-text">小橘今天学会了新技能——开门！现在家里没有它去不了的地方了 😂 这智商真是让人又爱又恨</div>

                        <div class="post-tags">
                            <span class="tag">#成长记录</span>
                            <span class="tag">#橘猫</span>
                            <span class="tag">#聪明猫咪</span>
                        </div>

                        <img src="https://images.unsplash.com/photo-1574158622682-e40e69881006?w=400&h=300&fit=crop"
                            alt="猫咪照片" class="post-image">

                        <div class="pet-info">
                            <img src="https://images.unsplash.com/photo-1574158622682-e40e69881006?w=64&h=64&fit=crop&crop=face"
                                alt="宠物头像" class="pet-avatar">
                            <div>
                                <div class="pet-name">小橘</div>
                                <div class="pet-breed">橘猫 · 1岁</div>
                            </div>
                        </div>
                    </div>

                    <div class="post-actions">
                        <div class="action-group">
                            <button class="action-btn">
                                <i class="far fa-heart"></i>
                                <span>89</span>
                            </button>
                            <button class="action-btn">
                                <i class="fas fa-comment"></i>
                                <span>15</span>
                            </button>
                            <button class="action-btn">
                                <i class="fas fa-share"></i>
                                <span>分享</span>
                            </button>
                        </div>
                        <button class="flower-btn">
                            <i class="fas fa-seedling"></i>
                            <span>献花</span>
                        </button>
                    </div>
                </div>

                <!-- 帖子3 -->
                <div class="post-card fade-in hover-lift">
                    <div class="post-header">
                        <img src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face"
                            alt="用户头像" class="avatar avatar-md">
                        <div class="user-info">
                            <div class="username">柴犬妈妈</div>
                            <div class="post-time">6小时前</div>
                        </div>
                        <button class="follow-btn">关注</button>
                    </div>

                    <div class="post-content">
                        <div class="post-text">豆豆今天满6个月啦！🎉 从小奶狗到现在的小帅哥，每一天都在给我惊喜。感谢萌宠记记录了我们这么多美好时光～</div>

                        <div class="post-tags">
                            <span class="tag">#里程碑</span>
                            <span class="tag">#柴犬</span>
                            <span class="tag">#6个月</span>
                        </div>

                        <img src="https://images.unsplash.com/photo-1583337130417-3346a1be7dee?w=400&h=300&fit=crop"
                            alt="柴犬照片" class="post-image">

                        <div class="pet-info">
                            <img src="https://images.unsplash.com/photo-1583337130417-3346a1be7dee?w=64&h=64&fit=crop&crop=face"
                                alt="宠物头像" class="pet-avatar">
                            <div>
                                <div class="pet-name">豆豆</div>
                                <div class="pet-breed">柴犬 · 6个月</div>
                            </div>
                        </div>
                    </div>

                    <div class="post-actions">
                        <div class="action-group">
                            <button class="action-btn">
                                <i class="far fa-heart"></i>
                                <span>156</span>
                            </button>
                            <button class="action-btn">
                                <i class="fas fa-comment"></i>
                                <span>32</span>
                            </button>
                            <button class="action-btn">
                                <i class="fas fa-share"></i>
                                <span>分享</span>
                            </button>
                        </div>
                        <button class="flower-btn">
                            <i class="fas fa-seedling"></i>
                            <span>献花</span>
                        </button>
                    </div>
                </div>

                <div class="loading-more">
                    <i class="fas fa-spinner fa-spin"></i>
                    正在加载更多内容...
                </div>
            </div>
        </div>

        <!-- 底部导航栏 -->
        <div class="tab-bar">
            <a href="home.html" class="tab-item active">
                <i class="fas fa-home"></i>
                <span>主页</span>
            </a>
            <a href="topics.html" class="tab-item">
                <i class="fas fa-hashtag"></i>
                <span>话题</span>
            </a>
            <a href="create.html" class="tab-item">
                <i class="fas fa-plus-circle"></i>
                <span>创作</span>
            </a>
            <a href="messages.html" class="tab-item">
                <i class="fas fa-bell"></i>
                <span>消息</span>
            </a>
            <a href="profile.html" class="tab-item">
                <i class="fas fa-user"></i>
                <span>我的</span>
            </a>
        </div>
    </div>

    <script>
        // Tab 切换功能
        document.querySelectorAll('.tab-nav-item').forEach(item => {
            item.addEventListener('click', function () {
                document.querySelectorAll('.tab-nav-item').forEach(tab => tab.classList.remove('active'));
                this.classList.add('active');
            });
        });

        // 点赞功能
        document.querySelectorAll('.action-btn').forEach(btn => {
            if (btn.querySelector('.fa-heart')) {
                btn.addEventListener('click', function () {
                    const icon = this.querySelector('i');
                    const count = this.querySelector('span');

                    if (this.classList.contains('liked')) {
                        this.classList.remove('liked');
                        icon.className = 'far fa-heart';
                        count.textContent = parseInt(count.textContent) - 1;
                    } else {
                        this.classList.add('liked');
                        icon.className = 'fas fa-heart';
                        count.textContent = parseInt(count.textContent) + 1;
                    }
                });
            }
        });

        // 帖子点击跳转
        document.querySelectorAll('.post-card').forEach(post => {
            post.addEventListener('click', function (e) {
                // 如果点击的是按钮，不跳转
                if (e.target.closest('.action-btn') || e.target.closest('.follow-btn') || e.target.closest('.flower-btn')) {
                    return;
                }
                window.location.href = 'post-detail.html';
            });
        });

        // 用户头像点击跳转到用户详情
        document.querySelectorAll('.avatar').forEach(avatar => {
            avatar.addEventListener('click', function (e) {
                e.stopPropagation();
                console.log('跳转到用户详情页');
                // window.location.href = 'user-profile.html';
            });
        });

        // 献花功能
        document.querySelectorAll('.flower-btn').forEach(btn => {
            btn.addEventListener('click', function (e) {
                e.stopPropagation();
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = 'scale(1)';
                }, 150);
            });
        });

        // 筛选面板功能
        function toggleFilterPanel() {
            const panel = document.getElementById('filter-panel');
            const filterBtn = document.querySelector('.filter-btn');

            if (panel.style.display === 'none') {
                panel.style.display = 'block';
                filterBtn.style.color = 'var(--primary)';
                filterBtn.style.background = 'var(--background-secondary)';
            } else {
                panel.style.display = 'none';
                filterBtn.style.color = 'var(--text-secondary)';
                filterBtn.style.background = 'none';
            }
        }

        // 筛选选项点击
        document.querySelectorAll('.filter-option').forEach(option => {
            option.addEventListener('click', function () {
                // 同一组内的选项互斥
                const section = this.closest('.filter-section');
                section.querySelectorAll('.filter-option').forEach(opt => opt.classList.remove('active'));
                this.classList.add('active');

                // 这里可以添加实际的筛选逻辑
                const filterType = this.dataset.sort || this.dataset.type;
                console.log('应用筛选:', filterType);
            });
        });

        // 点击其他区域关闭筛选面板
        document.addEventListener('click', function (e) {
            const panel = document.getElementById('filter-panel');
            const filterBtn = document.querySelector('.filter-btn');

            if (!e.target.closest('.filter-panel') && !e.target.closest('.filter-btn') && panel.style.display === 'block') {
                panel.style.display = 'none';
                filterBtn.style.color = 'var(--text-secondary)';
                filterBtn.style.background = 'none';
            }
        });
    </script>
</body>

</html>
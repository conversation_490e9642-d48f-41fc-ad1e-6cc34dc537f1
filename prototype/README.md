# 萌宠记 PetMoments - 高保真原型设计

## 🎯 项目概述

**萌宠记 (PetMoments)** 是一款专注于宠物生活记录与社交分享的移动端应用。本原型基于MVP功能规划文档设计，涵盖了宠物档案管理、内容创作发布、社交互动、话题广场、成长体系等核心功能。

## 🎨 设计特色

### 品牌色彩 - 绿色系
- **主色调**: `#4CAF50` (Material Design Green 500)
- **次要色**: `#81C784` (Material Design Green 300)
- **强调色**: `#A5D6A7` (Material Design Green 200)

### 设计理念
- **自然亲和**: 绿色系符合宠物和自然主题
- **现代简约**: 圆角设计，渐变效果，符合现代移动端审美
- **情感化设计**: 温馨的视觉风格，突出人宠情感连接

## 📱 原型页面

### 核心界面 (7个)

1. **主页Feed** (`home.html`)
   - 推荐/关注/同城三个Tab
   - 帖子卡片展示
   - 点赞、评论、分享、献花功能

2. **话题广场** (`topics.html`)
   - 搜索功能
   - 精选话题展示
   - 热门话题榜单
   - 热门标签云

3. **创作发布** (`create.html`)
   - 媒体上传区域
   - 文本编辑器
   - 宠物关联选择
   - 话题标签添加
   - 隐私设置

4. **消息中心** (`messages.html`)
   - 消息分类管理
   - 互动通知展示
   - 系统消息推送

5. **个人中心** (`profile.html`)
   - 用户资料展示
   - 宠物管理卡片
   - 成长体系展示
   - 功能菜单

6. **宠物档案** (`pet-profile.html`)
   - 宠物详细信息
   - 成长阶段可视化
   - 里程碑记录
   - 相册精选
   - 健康记录

7. **内容详情** (`post-detail.html`)
   - 帖子完整内容
   - 互动操作区
   - 评论区与回复
   - 底部评论输入

## 🛠 技术实现

### 技术栈
- **HTML5**: 语义化标签，无障碍设计
- **CSS3**: Flexbox/Grid布局，CSS变量，动画效果
- **JavaScript**: 原生JS实现交互功能
- **FontAwesome**: 图标库
- **Unsplash**: 高质量图片资源

### 特性
- **响应式设计**: 适配不同屏幕尺寸
- **iPhone 15 Pro适配**: 393×852px尺寸，圆角边框
- **模块化架构**: 独立HTML文件，公共样式
- **真实数据**: 使用真实图片，避免占位符

## 📂 文件结构

```
prototype/
├── index.html          # 主入口页面 (原型展示)
├── styles.css          # 公共样式文件
├── home.html           # 主页Feed界面
├── topics.html         # 话题广场界面
├── create.html         # 创作发布界面
├── messages.html       # 消息中心界面
├── profile.html        # 个人中心界面
├── pet-profile.html    # 宠物档案界面
├── post-detail.html    # 内容详情界面
├── test.html           # 测试页面
└── README.md           # 说明文档
```

## 🚀 使用方法

### 查看原型
1. 打开 `index.html` - 查看所有原型的总览展示
2. 打开 `test.html` - 查看各个页面的独立测试
3. 直接打开各个HTML文件 - 查看单独页面

### 浏览器兼容性
- Chrome 80+
- Safari 13+
- Firefox 75+
- Edge 80+

## 🎯 MVP功能覆盖

### ✅ 已实现功能
- 宠物档案管理 (多宠支持)
- 内容创作发布 (图文/视频)
- 社交互动功能 (点赞/评论/分享)
- 话题体系 (话题广场/投稿)
- 献花机制 (宠物专属互动)
- 成长体系 (用户等级/宠物里程碑)
- 消息通知系统
- 隐私设置管理

### 🔄 交互功能
- Tab切换动画
- 按钮点击反馈
- 悬停效果
- 表单验证
- 动态内容加载

## 📋 下一步计划

1. **用户测试**: 收集用户反馈，优化交互体验
2. **技术开发**: 基于原型进行前端开发
3. **后端API**: 设计数据接口和业务逻辑
4. **功能迭代**: 根据用户反馈添加新功能

## 📞 联系方式

如有问题或建议，请联系产品团队。

---

*本原型仅用于产品设计和用户测试，不包含实际的数据处理功能。*

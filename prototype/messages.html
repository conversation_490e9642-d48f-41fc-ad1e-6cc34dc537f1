<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>萌宠记 - 消息中心</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
    <style>
        /* 消息页面特定样式 */
        .messages-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 16px;
            background: var(--background);
            border-bottom: 1px solid var(--border-color);
        }

        .header-title {
            font-size: 20px;
            font-weight: 600;
            color: var(--text-primary);
        }

        .settings-btn {
            background: none;
            border: none;
            font-size: 18px;
            color: var(--text-secondary);
            cursor: pointer;
        }

        .message-tabs {
            display: flex;
            background: var(--background);
            border-bottom: 1px solid var(--border-color);
        }

        .message-tab {
            flex: 1;
            text-align: center;
            padding: 16px 0;
            color: var(--text-secondary);
            font-size: 15px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            position: relative;
        }

        .message-tab.active {
            color: var(--primary-color);
            font-weight: 600;
        }

        .message-tab.active::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 30px;
            height: 3px;
            background: var(--primary-color);
            border-radius: 2px;
        }

        .message-tab .badge {
            position: absolute;
            top: 8px;
            right: 20px;
            background: var(--primary-color);
            color: white;
            border-radius: 10px;
            padding: 2px 6px;
            font-size: 10px;
            font-weight: 600;
            min-width: 16px;
            text-align: center;
        }

        .messages-content {
            padding: 0 16px;
        }

        .message-item {
            display: flex;
            align-items: flex-start;
            padding: 16px 0;
            border-bottom: 1px solid var(--border-color);
            cursor: pointer;
            transition: background 0.2s;
        }

        .message-item:hover {
            background: var(--background-secondary);
            margin: 0 -16px;
            padding-left: 16px;
            padding-right: 16px;
            border-radius: 12px;
        }

        .message-item:last-child {
            border-bottom: none;
        }

        .message-avatar {
            position: relative;
            margin-right: 12px;
        }

        .message-icon {
            position: absolute;
            bottom: -2px;
            right: -2px;
            width: 20px;
            height: 20px;
            background: var(--primary-color);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            color: white;
            border: 2px solid var(--background);
        }

        .message-icon.like {
            background: #FF6B6B;
        }

        .message-icon.comment {
            background: #4ECDC4;
        }

        .message-icon.follow {
            background: #FFE66D;
            color: var(--text-primary);
        }

        .message-icon.flower {
            background: linear-gradient(135deg, #FFE66D, #FFD93D);
            color: var(--text-primary);
        }

        .message-icon.system {
            background: #95A5A6;
        }

        .message-content {
            flex: 1;
            min-width: 0;
        }

        .message-text {
            font-size: 14px;
            color: var(--text-primary);
            line-height: 1.4;
            margin-bottom: 4px;
        }

        .message-text .username {
            font-weight: 600;
            color: var(--primary-color);
        }

        .message-meta {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 12px;
            color: var(--text-secondary);
        }

        .message-time {
            flex: 1;
        }

        .unread-dot {
            width: 8px;
            height: 8px;
            background: var(--primary-color);
            border-radius: 50%;
        }

        .message-preview {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            object-fit: cover;
            margin-left: 12px;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: var(--text-secondary);
        }

        .empty-state i {
            font-size: 48px;
            margin-bottom: 16px;
            opacity: 0.5;
        }

        .empty-state h3 {
            font-size: 16px;
            font-weight: 500;
            margin-bottom: 8px;
        }

        .empty-state p {
            font-size: 14px;
            line-height: 1.4;
        }

        .system-message {
            background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
            color: white;
            border-radius: 16px;
            padding: 16px;
            margin: 16px 0;
            position: relative;
            overflow: hidden;
        }

        .system-message::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 100px;
            height: 100px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
        }

        .system-message h4 {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .system-message p {
            font-size: 14px;
            opacity: 0.9;
            line-height: 1.4;
        }

        .mark-all-read {
            background: var(--background-secondary);
            color: var(--text-secondary);
            border: none;
            border-radius: 20px;
            padding: 8px 16px;
            font-size: 12px;
            font-weight: 500;
            cursor: pointer;
            margin: 16px 0;
        }
    </style>
</head>

<body>
    <div class="iphone-container">
        <!-- iOS 状态栏 -->
        <div class="status-bar">
            <div class="status-bar-notch"></div>
            <div class="status-bar-left">
                <span>中国移动</span>
                <i class="fas fa-signal"></i>
            </div>
            <div class="status-bar-center status-bar-time">9:41</div>
            <div class="status-bar-right">
                <i class="fas fa-wifi"></i>
                <i class="fas fa-battery-three-quarters"></i>
            </div>
        </div>

        <!-- 主内容区域 -->
        <div class="main-content">
            <!-- 消息页面头部 -->
            <div class="messages-header">
                <h1 class="header-title">消息</h1>
                <button class="settings-btn">
                    <i class="fas fa-cog"></i>
                </button>
            </div>

            <!-- 消息分类标签 -->
            <div class="message-tabs">
                <div class="message-tab active" data-tab="all">
                    全部
                    <span class="badge">5</span>
                </div>
                <div class="message-tab" data-tab="interactions">
                    互动
                    <span class="badge">3</span>
                </div>
                <div class="message-tab" data-tab="follows">
                    关注
                    <span class="badge">1</span>
                </div>
                <div class="message-tab" data-tab="system">
                    系统
                    <span class="badge">1</span>
                </div>
            </div>

            <div class="messages-content">
                <!-- 系统消息横幅 -->
                <div class="system-message">
                    <h4><i class="fas fa-gift"></i> 新手福利</h4>
                    <p>恭喜你加入萌宠记大家庭！完成首次发布可获得新手徽章，快去记录你和毛孩子的美好时光吧～</p>
                </div>

                <button class="mark-all-read">全部标记为已读</button>

                <!-- 消息列表 -->
                <div class="message-item">
                    <div class="message-avatar">
                        <img src="https://images.unsplash.com/photo-1494790108755-2616c9c9b8d4?w=48&h=48&fit=crop&crop=face"
                            alt="用户头像" class="avatar avatar-md">
                        <div class="message-icon flower">
                            <i class="fas fa-seedling"></i>
                        </div>
                    </div>
                    <div class="message-content">
                        <div class="message-text">
                            <span class="username">萌宠小雅</span> 给你的宠物 <span class="username">球球</span> 献了花
                        </div>
                        <div class="message-meta">
                            <span class="message-time">2分钟前</span>
                            <div class="unread-dot"></div>
                        </div>
                    </div>
                    <img src="https://images.unsplash.com/photo-1552053831-71594a27632d?w=40&h=40&fit=crop&crop=face"
                        alt="宠物照片" class="message-preview">
                </div>

                <div class="message-item">
                    <div class="message-avatar">
                        <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=48&h=48&fit=crop&crop=face"
                            alt="用户头像" class="avatar avatar-md">
                        <div class="message-icon like">
                            <i class="fas fa-heart"></i>
                        </div>
                    </div>
                    <div class="message-content">
                        <div class="message-text">
                            <span class="username">猫咪控阿明</span> 赞了你的动态
                        </div>
                        <div class="message-meta">
                            <span class="message-time">15分钟前</span>
                            <div class="unread-dot"></div>
                        </div>
                    </div>
                    <img src="https://images.unsplash.com/photo-1583337130417-3346a1be7dee?w=40&h=40&fit=crop"
                        alt="动态预览" class="message-preview">
                </div>

                <div class="message-item">
                    <div class="message-avatar">
                        <img src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=48&h=48&fit=crop&crop=face"
                            alt="用户头像" class="avatar avatar-md">
                        <div class="message-icon comment">
                            <i class="fas fa-comment"></i>
                        </div>
                    </div>
                    <div class="message-content">
                        <div class="message-text">
                            <span class="username">柴犬妈妈</span> 评论了你的动态：太可爱了！我家豆豆也是这样的
                        </div>
                        <div class="message-meta">
                            <span class="message-time">1小时前</span>
                            <div class="unread-dot"></div>
                        </div>
                    </div>
                    <img src="https://images.unsplash.com/photo-1552053831-71594a27632d?w=40&h=40&fit=crop" alt="动态预览"
                        class="message-preview">
                </div>

                <div class="message-item">
                    <div class="message-avatar">
                        <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=48&h=48&fit=crop&crop=face"
                            alt="用户头像" class="avatar avatar-md">
                        <div class="message-icon follow">
                            <i class="fas fa-user-plus"></i>
                        </div>
                    </div>
                    <div class="message-content">
                        <div class="message-text">
                            <span class="username">宠物摄影师小李</span> 关注了你
                        </div>
                        <div class="message-meta">
                            <span class="message-time">3小时前</span>
                            <div class="unread-dot"></div>
                        </div>
                    </div>
                </div>

                <div class="message-item">
                    <div class="message-avatar">
                        <img src="https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=48&h=48&fit=crop&crop=face"
                            alt="系统头像" class="avatar avatar-md">
                        <div class="message-icon system">
                            <i class="fas fa-bell"></i>
                        </div>
                    </div>
                    <div class="message-content">
                        <div class="message-text">
                            你的宠物 <span class="username">球球</span> 今日收到了 <span class="username">5朵花</span>，在金毛圈日榜排名第8位！
                        </div>
                        <div class="message-meta">
                            <span class="message-time">昨天</span>
                        </div>
                    </div>
                </div>

                <div class="message-item">
                    <div class="message-avatar">
                        <img src="https://images.unsplash.com/photo-1517849845537-4d257902454a?w=48&h=48&fit=crop&crop=face"
                            alt="用户头像" class="avatar avatar-md">
                        <div class="message-icon like">
                            <i class="fas fa-heart"></i>
                        </div>
                    </div>
                    <div class="message-content">
                        <div class="message-text">
                            <span class="username">边牧达人</span> 和其他 <span class="username">12人</span> 赞了你的动态
                        </div>
                        <div class="message-meta">
                            <span class="message-time">昨天</span>
                        </div>
                    </div>
                    <img src="https://images.unsplash.com/photo-1574158622682-e40e69881006?w=40&h=40&fit=crop"
                        alt="动态预览" class="message-preview">
                </div>

                <div class="message-item">
                    <div class="message-avatar">
                        <img src="https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=48&h=48&fit=crop&crop=face"
                            alt="系统头像" class="avatar avatar-md">
                        <div class="message-icon system">
                            <i class="fas fa-trophy"></i>
                        </div>
                    </div>
                    <div class="message-content">
                        <div class="message-text">
                            恭喜！你获得了 <span class="username">"活跃用户"</span> 徽章，继续保持哦～
                        </div>
                        <div class="message-meta">
                            <span class="message-time">2天前</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部导航栏 -->
        <div class="tab-bar">
            <a href="#" class="tab-item">
                <i class="fas fa-home"></i>
                <span>主页</span>
            </a>
            <a href="#" class="tab-item">
                <i class="fas fa-hashtag"></i>
                <span>话题</span>
            </a>
            <a href="#" class="tab-item">
                <i class="fas fa-plus-circle"></i>
                <span>创作</span>
            </a>
            <a href="#" class="tab-item active">
                <i class="fas fa-bell"></i>
                <span>消息</span>
            </a>
            <a href="#" class="tab-item">
                <i class="fas fa-user"></i>
                <span>我的</span>
            </a>
        </div>
    </div>

    <script>
        // 消息标签切换
        document.querySelectorAll('.message-tab').forEach(tab => {
            tab.addEventListener('click', function () {
                document.querySelectorAll('.message-tab').forEach(t => t.classList.remove('active'));
                this.classList.add('active');

                // 这里可以根据不同标签过滤消息
                const tabType = this.dataset.tab;
                filterMessages(tabType);
            });
        });

        function filterMessages(type) {
            // 简单的过滤逻辑演示
            const messages = document.querySelectorAll('.message-item');
            messages.forEach(message => {
                message.style.display = 'flex'; // 显示所有消息
            });
        }

        // 消息项点击
        document.querySelectorAll('.message-item').forEach(item => {
            item.addEventListener('click', function () {
                // 移除未读标记
                const unreadDot = this.querySelector('.unread-dot');
                if (unreadDot) {
                    unreadDot.remove();
                    updateBadgeCount();
                }

                // 添加点击效果
                this.style.transform = 'scale(0.98)';
                setTimeout(() => {
                    this.style.transform = 'scale(1)';
                }, 150);
            });
        });

        // 全部标记为已读
        document.querySelector('.mark-all-read').addEventListener('click', function () {
            document.querySelectorAll('.unread-dot').forEach(dot => dot.remove());
            document.querySelectorAll('.badge').forEach(badge => badge.textContent = '0');
            this.style.display = 'none';
        });

        function updateBadgeCount() {
            const unreadCount = document.querySelectorAll('.unread-dot').length;
            document.querySelectorAll('.badge').forEach(badge => {
                badge.textContent = unreadCount;
                if (unreadCount === 0) {
                    badge.style.display = 'none';
                }
            });
        }

        // 设置按钮点击
        document.querySelector('.settings-btn').addEventListener('click', function () {
            this.style.transform = 'rotate(90deg)';
            setTimeout(() => {
                this.style.transform = 'rotate(0deg)';
            }, 200);
        });
    </script>
</body>

</html>
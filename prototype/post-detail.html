<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>萌宠记 - 内容详情</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
    <style>
        /* 内容详情特定样式 */
        .detail-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 16px;
            background: var(--background);
            border-bottom: 1px solid var(--border-color);
        }

        .back-btn {
            background: none;
            border: none;
            font-size: 18px;
            color: var(--text-primary);
            cursor: pointer;
        }

        .more-btn {
            background: none;
            border: none;
            font-size: 18px;
            color: var(--text-secondary);
            cursor: pointer;
        }

        .post-detail {
            padding: 16px;
        }

        .author-info {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 16px;
        }

        .author-details {
            flex: 1;
        }

        .author-name {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 2px;
        }

        .post-time {
            font-size: 12px;
            color: var(--text-secondary);
        }

        .follow-btn {
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 16px;
            padding: 6px 16px;
            font-size: 12px;
            font-weight: 500;
            cursor: pointer;
        }

        .post-content {
            margin-bottom: 16px;
        }

        .post-text {
            font-size: 16px;
            line-height: 1.5;
            color: var(--text-primary);
            margin-bottom: 16px;
        }

        .post-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-bottom: 16px;
        }

        .tag {
            background: var(--background-secondary);
            color: var(--primary-color);
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            cursor: pointer;
        }

        .post-image {
            width: 100%;
            border-radius: 16px;
            margin-bottom: 16px;
        }

        .pet-card {
            background: var(--background-secondary);
            border-radius: 16px;
            padding: 16px;
            margin-bottom: 16px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .pet-card:hover {
            background: rgba(255, 107, 107, 0.1);
        }

        .pet-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .pet-avatar {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            object-fit: cover;
        }

        .pet-name {
            font-size: 15px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 2px;
        }

        .pet-breed {
            font-size: 12px;
            color: var(--text-secondary);
        }

        .flower-count {
            margin-left: auto;
            display: flex;
            align-items: center;
            gap: 4px;
            color: var(--accent-color);
            font-size: 12px;
            font-weight: 600;
        }

        .post-actions {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 16px;
            border-top: 1px solid var(--border-color);
            border-bottom: 1px solid var(--border-color);
            background: var(--background);
        }

        .action-group {
            display: flex;
            gap: 24px;
        }

        .action-btn {
            display: flex;
            align-items: center;
            gap: 6px;
            background: none;
            border: none;
            color: var(--text-secondary);
            font-size: 14px;
            cursor: pointer;
            transition: color 0.2s;
        }

        .action-btn.liked {
            color: var(--primary-color);
        }

        .action-btn i {
            font-size: 18px;
        }

        .flower-btn {
            background: linear-gradient(135deg, var(--accent-color), #FFFDE7);
            color: var(--text-primary);
            border: none;
            border-radius: 20px;
            padding: 8px 16px;
            font-size: 12px;
            font-weight: 600;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .comments-section {
            padding: 16px;
        }

        .comments-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 16px;
        }

        .comments-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
        }

        .sort-btn {
            background: none;
            border: none;
            color: var(--text-secondary);
            font-size: 12px;
            cursor: pointer;
        }

        .comment-item {
            display: flex;
            align-items: flex-start;
            gap: 12px;
            margin-bottom: 16px;
            padding-bottom: 16px;
            border-bottom: 1px solid var(--border-color);
        }

        .comment-item:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }

        .comment-content {
            flex: 1;
        }

        .comment-author {
            font-size: 14px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 4px;
        }

        .comment-text {
            font-size: 14px;
            line-height: 1.4;
            color: var(--text-primary);
            margin-bottom: 8px;
        }

        .comment-meta {
            display: flex;
            align-items: center;
            gap: 16px;
            font-size: 12px;
            color: var(--text-secondary);
        }

        .comment-time {
            cursor: pointer;
        }

        .comment-like {
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .comment-reply {
            cursor: pointer;
        }

        .reply-item {
            margin-left: 40px;
            margin-top: 12px;
            padding-top: 12px;
            border-top: 1px solid var(--border-color);
        }

        .comment-input {
            position: absolute;
            bottom: 83px;
            left: 0;
            right: 0;
            background: var(--background);
            border-top: 1px solid var(--border-color);
            padding: 12px 16px;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .comment-input input {
            flex: 1;
            padding: 8px 16px;
            border: 1px solid var(--border-color);
            border-radius: 20px;
            font-size: 14px;
            outline: none;
        }

        .send-btn {
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 50%;
            width: 36px;
            height: 36px;
            font-size: 14px;
            cursor: pointer;
        }

        .send-btn:disabled {
            background: var(--text-secondary);
            cursor: not-allowed;
        }

        .main-content {
            padding-bottom: 80px;
            /* 为评论输入框留出空间 */
        }

        /* 分享弹窗样式 */
        .share-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 2000;
        }

        .modal-backdrop {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
        }

        .share-content {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: var(--background);
            border-radius: 20px 20px 0 0;
            padding: 20px;
            animation: slideUp 0.3s ease;
        }

        @keyframes slideUp {
            from {
                transform: translateY(100%);
            }

            to {
                transform: translateY(0);
            }
        }

        .share-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20px;
        }

        .share-header h3 {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 18px;
            color: var(--text-secondary);
            cursor: pointer;
            padding: 4px;
        }

        .share-options {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
        }

        .share-option {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
            cursor: pointer;
            transition: transform 0.2s;
        }

        .share-option:hover {
            transform: scale(1.05);
        }

        .share-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
        }

        .share-option span {
            font-size: 12px;
            color: var(--text-primary);
        }
    </style>
</head>

<body>
    <div class="iphone-container">
        <!-- iOS 状态栏 -->
        <div class="status-bar">
            <div class="status-left">
                <span>9:41</span>
            </div>
            <div class="status-right">
                <i class="fas fa-signal"></i>
                <i class="fas fa-wifi"></i>
                <div class="battery">
                    <div class="battery-fill"></div>
                </div>
            </div>
        </div>

        <!-- 主内容区域 -->
        <div class="main-content">
            <!-- 详情页头部 -->
            <div class="detail-header">
                <button class="back-btn" onclick="window.location.href='home.html'">
                    <i class="fas fa-arrow-left"></i>
                </button>
                <button class="more-btn">
                    <i class="fas fa-ellipsis-h"></i>
                </button>
            </div>

            <!-- 帖子详情 -->
            <div class="post-detail">
                <div class="author-info">
                    <img src="https://images.unsplash.com/photo-1494790108755-2616c9c9b8d4?w=48&h=48&fit=crop&crop=face"
                        alt="用户头像" class="avatar avatar-md">
                    <div class="author-details">
                        <div class="author-name">萌宠小雅</div>
                        <div class="post-time">2小时前</div>
                    </div>
                    <button class="follow-btn">关注</button>
                </div>

                <div class="post-content">
                    <div class="post-text">
                        今天带球球去公园玩，它第一次看到这么多小朋友，兴奋得不得了！看它这个开心的样子，我的心都要化了 🥰
                        <br><br>
                        记得刚把球球接回家的时候，它还是个小奶狗，现在已经长成了这么帅气的大狗狗。时间过得真快，每一天都在给我惊喜。
                        <br><br>
                        感谢萌宠记让我记录下了这么多美好的时光，希望和更多铲屎官分享养宠的快乐～
                    </div>

                    <div class="post-tags">
                        <span class="tag">#萌宠日常</span>
                        <span class="tag">#金毛</span>
                        <span class="tag">#公园遛狗</span>
                        <span class="tag">#成长记录</span>
                    </div>

                    <img src="https://images.unsplash.com/photo-1552053831-71594a27632d?w=400&h=300&fit=crop" alt="宠物照片"
                        class="post-image">

                    <div class="pet-card">
                        <div class="pet-info">
                            <img src="https://images.unsplash.com/photo-1552053831-71594a27632d?w=96&h=96&fit=crop&crop=face"
                                alt="宠物头像" class="pet-avatar">
                            <div>
                                <div class="pet-name">球球</div>
                                <div class="pet-breed">金毛寻回犬 · 2岁</div>
                            </div>
                            <div class="flower-count">
                                <i class="fas fa-seedling"></i>
                                <span>今日收花 5</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 互动操作 -->
            <div class="post-actions">
                <div class="action-group">
                    <button class="action-btn liked">
                        <i class="fas fa-heart"></i>
                        <span>128</span>
                    </button>
                    <button class="action-btn">
                        <i class="fas fa-comment"></i>
                        <span>23</span>
                    </button>
                    <button class="action-btn" onclick="showShareModal()">
                        <i class="fas fa-share"></i>
                        <span>分享</span>
                    </button>
                </div>
                <button class="flower-btn">
                    <i class="fas fa-seedling"></i>
                    <span>献花</span>
                </button>
            </div>

            <!-- 评论区 -->
            <div class="comments-section">
                <div class="comments-header">
                    <h3 class="comments-title">评论 23</h3>
                    <button class="sort-btn">最新 <i class="fas fa-chevron-down"></i></button>
                </div>

                <div class="comment-item">
                    <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=32&h=32&fit=crop&crop=face"
                        alt="评论者头像" class="avatar avatar-sm">
                    <div class="comment-content">
                        <div class="comment-author">猫咪控阿明</div>
                        <div class="comment-text">太可爱了！我家小橘也是这样，看到小朋友就特别兴奋，恨不得和每个人都玩 😂</div>
                        <div class="comment-meta">
                            <span class="comment-time">1小时前</span>
                            <span class="comment-like">
                                <i class="far fa-heart"></i>
                                <span>12</span>
                            </span>
                            <span class="comment-reply">回复</span>
                        </div>
                    </div>
                </div>

                <div class="comment-item">
                    <img src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=32&h=32&fit=crop&crop=face"
                        alt="评论者头像" class="avatar avatar-sm">
                    <div class="comment-content">
                        <div class="comment-author">柴犬妈妈</div>
                        <div class="comment-text">球球真的好帅！金毛的性格就是这么温顺友好，特别适合和小朋友相处</div>
                        <div class="comment-meta">
                            <span class="comment-time">45分钟前</span>
                            <span class="comment-like">
                                <i class="fas fa-heart"></i>
                                <span>8</span>
                            </span>
                            <span class="comment-reply">回复</span>
                        </div>

                        <div class="reply-item">
                            <div class="comment-item">
                                <img src="https://images.unsplash.com/photo-1494790108755-2616c9c9b8d4?w=32&h=32&fit=crop&crop=face"
                                    alt="作者头像" class="avatar avatar-sm">
                                <div class="comment-content">
                                    <div class="comment-author">萌宠小雅 <span
                                            style="color: var(--primary-color); font-size: 10px;">作者</span></div>
                                    <div class="comment-text">是的！球球特别喜欢小朋友，每次看到都要上去打招呼</div>
                                    <div class="comment-meta">
                                        <span class="comment-time">30分钟前</span>
                                        <span class="comment-like">
                                            <i class="far fa-heart"></i>
                                            <span>3</span>
                                        </span>
                                        <span class="comment-reply">回复</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="comment-item">
                    <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=32&h=32&fit=crop&crop=face"
                        alt="评论者头像" class="avatar avatar-sm">
                    <div class="comment-content">
                        <div class="comment-author">宠物摄影师小李</div>
                        <div class="comment-text">这张照片拍得真好！球球的表情太生动了，可以看出它真的很开心</div>
                        <div class="comment-meta">
                            <span class="comment-time">20分钟前</span>
                            <span class="comment-like">
                                <i class="far fa-heart"></i>
                                <span>5</span>
                            </span>
                            <span class="comment-reply">回复</span>
                        </div>
                    </div>
                </div>

                <div class="comment-item">
                    <img src="https://images.unsplash.com/photo-1517849845537-4d257902454a?w=32&h=32&fit=crop&crop=face"
                        alt="评论者头像" class="avatar avatar-sm">
                    <div class="comment-content">
                        <div class="comment-author">边牧达人</div>
                        <div class="comment-text">羡慕！我家边牧看到小朋友就想去放羊 😅 每次都要拉着不让它过去</div>
                        <div class="comment-meta">
                            <span class="comment-time">10分钟前</span>
                            <span class="comment-like">
                                <i class="far fa-heart"></i>
                                <span>15</span>
                            </span>
                            <span class="comment-reply">回复</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 评论输入框 -->
        <div class="comment-input">
            <input type="text" placeholder="写下你的评论..." id="comment-text">
            <button class="send-btn" id="send-btn" disabled>
                <i class="fas fa-paper-plane"></i>
            </button>
        </div>
    </div>

    <!-- 分享弹窗 -->
    <div class="share-modal" id="share-modal" style="display: none;">
        <div class="modal-backdrop" onclick="hideShareModal()"></div>
        <div class="share-content">
            <div class="share-header">
                <h3>分享到</h3>
                <button class="close-btn" onclick="hideShareModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="share-options">
                <div class="share-option" data-platform="wechat">
                    <div class="share-icon" style="background: #07c160;">
                        <i class="fab fa-weixin"></i>
                    </div>
                    <span>微信</span>
                </div>
                <div class="share-option" data-platform="moments">
                    <div class="share-icon" style="background: #07c160;">
                        <i class="fas fa-circle"></i>
                    </div>
                    <span>朋友圈</span>
                </div>
                <div class="share-option" data-platform="weibo">
                    <div class="share-icon" style="background: #e6162d;">
                        <i class="fab fa-weibo"></i>
                    </div>
                    <span>微博</span>
                </div>
                <div class="share-option" data-platform="qq">
                    <div class="share-icon" style="background: #12b7f5;">
                        <i class="fab fa-qq"></i>
                    </div>
                    <span>QQ</span>
                </div>
                <div class="share-option" data-platform="copy">
                    <div class="share-icon" style="background: var(--text-secondary);">
                        <i class="fas fa-copy"></i>
                    </div>
                    <span>复制链接</span>
                </div>
                <div class="share-option" data-platform="more">
                    <div class="share-icon" style="background: var(--text-secondary);">
                        <i class="fas fa-ellipsis-h"></i>
                    </div>
                    <span>更多</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 评论输入功能
        const commentInput = document.getElementById('comment-text');
        const sendBtn = document.getElementById('send-btn');

        commentInput.addEventListener('input', function () {
            if (this.value.trim().length > 0) {
                sendBtn.disabled = false;
                sendBtn.style.background = 'var(--primary-color)';
            } else {
                sendBtn.disabled = true;
                sendBtn.style.background = 'var(--text-secondary)';
            }
        });

        // 点赞功能
        document.querySelectorAll('.action-btn').forEach(btn => {
            if (btn.querySelector('.fa-heart')) {
                btn.addEventListener('click', function () {
                    const icon = this.querySelector('i');
                    const count = this.querySelector('span');

                    if (this.classList.contains('liked')) {
                        this.classList.remove('liked');
                        icon.className = 'far fa-heart';
                        count.textContent = parseInt(count.textContent) - 1;
                    } else {
                        this.classList.add('liked');
                        icon.className = 'fas fa-heart';
                        count.textContent = parseInt(count.textContent) + 1;
                    }
                });
            }
        });

        // 评论点赞功能
        document.querySelectorAll('.comment-like').forEach(like => {
            like.addEventListener('click', function () {
                const icon = this.querySelector('i');
                const count = this.querySelector('span');

                if (icon.classList.contains('far')) {
                    icon.className = 'fas fa-heart';
                    this.style.color = 'var(--primary-color)';
                    count.textContent = parseInt(count.textContent) + 1;
                } else {
                    icon.className = 'far fa-heart';
                    this.style.color = 'var(--text-secondary)';
                    count.textContent = parseInt(count.textContent) - 1;
                }
            });
        });

        // 献花功能
        document.querySelector('.flower-btn').addEventListener('click', function () {
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = 'scale(1)';
            }, 150);
        });

        // 标签点击
        document.querySelectorAll('.tag').forEach(tag => {
            tag.addEventListener('click', function () {
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = 'scale(1)';
                }, 150);
            });
        });

        // 宠物卡片点击
        document.querySelector('.pet-card').addEventListener('click', function () {
            this.style.transform = 'scale(0.98)';
            setTimeout(() => {
                this.style.transform = 'scale(1)';
            }, 150);
        });

        // 返回按钮
        document.querySelector('.back-btn').addEventListener('click', function () {
            this.style.transform = 'scale(0.9)';
            setTimeout(() => {
                this.style.transform = 'scale(1)';
            }, 150);
        });

        // 发送评论
        sendBtn.addEventListener('click', function () {
            if (!this.disabled) {
                const text = commentInput.value.trim();
                if (text) {
                    // 这里应该发送评论到服务器
                    commentInput.value = '';
                    this.disabled = true;
                    this.style.background = 'var(--text-secondary)';

                    // 简单的成功反馈
                    this.innerHTML = '<i class="fas fa-check"></i>';
                    setTimeout(() => {
                        this.innerHTML = '<i class="fas fa-paper-plane"></i>';
                    }, 1000);
                }
            }
        });

        // 分享功能
        function showShareModal() {
            document.getElementById('share-modal').style.display = 'block';
        }

        function hideShareModal() {
            document.getElementById('share-modal').style.display = 'none';
        }

        // 分享选项点击
        document.querySelectorAll('.share-option').forEach(option => {
            option.addEventListener('click', function () {
                const platform = this.dataset.platform;

                switch (platform) {
                    case 'copy':
                        // 复制链接
                        if (navigator.clipboard) {
                            navigator.clipboard.writeText(window.location.href).then(() => {
                                alert('链接已复制到剪贴板');
                            });
                        } else {
                            alert('复制链接功能不支持');
                        }
                        break;
                    case 'wechat':
                        alert('分享到微信');
                        break;
                    case 'moments':
                        alert('分享到朋友圈');
                        break;
                    case 'weibo':
                        alert('分享到微博');
                        break;
                    case 'qq':
                        alert('分享到QQ');
                        break;
                    default:
                        alert('分享到' + this.querySelector('span').textContent);
                }

                hideShareModal();
            });
        });
    </script>
</body>

</html>
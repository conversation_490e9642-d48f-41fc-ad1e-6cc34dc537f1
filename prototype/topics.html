<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>萌宠记 - 话题广场</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
    <style>
        /* 话题页面特定样式 */
        .search-bar {
            padding: 16px;
            background: var(--background);
            border-bottom: 1px solid var(--border-color);
        }

        .search-input {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid var(--border-color);
            border-radius: 20px;
            font-size: 14px;
            background: var(--background-secondary);
            outline: none;
        }

        .search-input::placeholder {
            color: var(--text-secondary);
        }

        .topic-nav {
            display: flex;
            padding: 16px;
            gap: 12px;
            background: var(--background);
            border-bottom: 1px solid var(--border-color);
            overflow-x: auto;
        }

        .topic-nav-item {
            background: var(--background-secondary);
            color: var(--text-secondary);
            border: none;
            border-radius: 20px;
            padding: 8px 16px;
            font-size: 13px;
            font-weight: 500;
            cursor: pointer;
            white-space: nowrap;
            transition: all 0.2s;
        }

        .topic-nav-item.active {
            background: var(--primary-color);
            color: white;
        }

        .featured-topics {
            padding: 16px;
            background: var(--background);
        }

        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .featured-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            margin-bottom: 20px;
        }

        .featured-topic {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border-radius: 16px;
            padding: 20px 16px;
            color: white;
            text-decoration: none;
            position: relative;
            overflow: hidden;
        }

        .featured-topic::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 100px;
            height: 100px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
        }

        .featured-topic h3 {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .featured-topic p {
            font-size: 12px;
            opacity: 0.9;
            margin-bottom: 8px;
        }

        .featured-topic .stats {
            font-size: 11px;
            opacity: 0.8;
        }

        .hot-topics {
            padding: 0 16px 16px;
        }

        .topic-list {
            background: var(--background);
            border-radius: 16px;
            overflow: hidden;
            box-shadow: var(--shadow);
        }

        .topic-item {
            display: flex;
            align-items: center;
            padding: 16px;
            border-bottom: 1px solid var(--border-color);
            text-decoration: none;
            color: inherit;
            transition: background 0.2s;
        }

        .topic-item:last-child {
            border-bottom: none;
        }

        .topic-item:hover {
            background: var(--background-secondary);
        }

        .topic-rank {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: var(--primary-color);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: 600;
            margin-right: 12px;
        }

        .topic-rank.gold {
            background: linear-gradient(135deg, #FFD700, #FFA500);
        }

        .topic-rank.silver {
            background: linear-gradient(135deg, #C0C0C0, #A0A0A0);
        }

        .topic-rank.bronze {
            background: linear-gradient(135deg, #CD7F32, #B8860B);
        }

        .topic-info {
            flex: 1;
        }

        .topic-name {
            font-size: 15px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 4px;
        }

        .topic-desc {
            font-size: 12px;
            color: var(--text-secondary);
            margin-bottom: 4px;
        }

        .topic-stats {
            font-size: 11px;
            color: var(--text-secondary);
        }

        .topic-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: var(--background-secondary);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            color: var(--primary-color);
            margin-left: 12px;
        }

        .create-topic-btn {
            position: fixed;
            bottom: 100px;
            right: 20px;
            width: 56px;
            height: 56px;
            background: linear-gradient(135deg, var(--primary-color), #FFA726);
            border: none;
            border-radius: 50%;
            color: white;
            font-size: 24px;
            cursor: pointer;
            box-shadow: 0 4px 16px rgba(255, 183, 77, 0.4);
            transition: all 0.2s;
        }

        .create-topic-btn:hover {
            transform: scale(1.1);
        }

        .trending-section {
            padding: 16px;
            background: var(--background-secondary);
        }

        .trending-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .trending-tag {
            background: var(--background);
            color: var(--text-primary);
            padding: 8px 12px;
            border-radius: 16px;
            font-size: 12px;
            font-weight: 500;
            text-decoration: none;
            border: 1px solid var(--border-color);
            transition: all 0.2s;
        }

        .trending-tag:hover {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .trending-tag .fire {
            color: #FFB74D;
            margin-right: 4px;
        }
    </style>
</head>

<body>
    <div class="iphone-container">
        <!-- iOS 状态栏 -->
        <div class="status-bar">
            <div class="status-left">
                <span>9:41</span>
            </div>
            <div class="status-right">
                <i class="fas fa-signal"></i>
                <i class="fas fa-wifi"></i>
                <div class="battery">
                    <div class="battery-fill"></div>
                </div>
            </div>
        </div>

        <!-- 主内容区域 -->
        <div class="main-content">
            <!-- 搜索栏 -->
            <div class="search-bar">
                <input type="text" class="search-input" placeholder="搜索话题、用户、内容..."
                    onclick="window.location.href='search-results.html'">
            </div>

            <!-- 话题导航 -->
            <div class="topic-nav">
                <button class="topic-nav-item active">全部</button>
                <button class="topic-nav-item">萌宠日常</button>
                <button class="topic-nav-item">成长记录</button>
                <button class="topic-nav-item">品种圈</button>
                <button class="topic-nav-item">同城</button>
                <button class="topic-nav-item">训练技巧</button>
                <button class="topic-nav-item">健康饮食</button>
            </div>

            <!-- 精选话题 -->
            <div class="featured-topics">
                <h2 class="section-title">
                    <i class="fas fa-star"></i>
                    精选话题
                </h2>
                <div class="featured-grid">
                    <a href="#" class="featured-topic">
                        <h3>#春日遛狗</h3>
                        <p>记录春天与毛孩子的美好时光</p>
                        <div class="stats">1.2万参与 · 今日新增328</div>
                    </a>
                    <a href="#" class="featured-topic"
                        style="background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));">
                        <h3>#新手养宠</h3>
                        <p>新手铲屎官必看攻略</p>
                        <div class="stats">8.5k参与 · 今日新增156</div>
                    </a>
                </div>
            </div>

            <!-- 热门话题榜 -->
            <div class="hot-topics">
                <h2 class="section-title">
                    <i class="fas fa-fire"></i>
                    热门话题榜
                </h2>
                <div class="topic-list">
                    <a href="#" class="topic-item hover-scale">
                        <div class="topic-rank gold">1</div>
                        <div class="topic-info">
                            <div class="topic-name">#萌宠日常</div>
                            <div class="topic-desc">分享毛孩子的日常生活点滴</div>
                            <div class="topic-stats">15.6万参与 · 今日新增1.2k</div>
                        </div>
                        <div class="topic-icon">
                            <i class="fas fa-paw"></i>
                        </div>
                    </a>

                    <a href="#" class="topic-item">
                        <div class="topic-rank silver">2</div>
                        <div class="topic-info">
                            <div class="topic-name">#成长记录</div>
                            <div class="topic-desc">记录宠物成长的每一个瞬间</div>
                            <div class="topic-stats">12.3万参与 · 今日新增856</div>
                        </div>
                        <div class="topic-icon">
                            <i class="fas fa-seedling"></i>
                        </div>
                    </a>

                    <a href="#" class="topic-item">
                        <div class="topic-rank bronze">3</div>
                        <div class="topic-info">
                            <div class="topic-name">#金毛圈</div>
                            <div class="topic-desc">金毛寻回犬专属交流圈</div>
                            <div class="topic-stats">9.8万参与 · 今日新增623</div>
                        </div>
                        <div class="topic-icon">
                            <i class="fas fa-dog"></i>
                        </div>
                    </a>

                    <a href="#" class="topic-item">
                        <div class="topic-rank">4</div>
                        <div class="topic-info">
                            <div class="topic-name">#猫咪圈</div>
                            <div class="topic-desc">喵星人的专属领地</div>
                            <div class="topic-stats">8.7万参与 · 今日新增445</div>
                        </div>
                        <div class="topic-icon">
                            <i class="fas fa-cat"></i>
                        </div>
                    </a>

                    <a href="#" class="topic-item">
                        <div class="topic-rank">5</div>
                        <div class="topic-info">
                            <div class="topic-name">#训练技巧</div>
                            <div class="topic-desc">分享宠物训练心得和技巧</div>
                            <div class="topic-stats">6.2万参与 · 今日新增312</div>
                        </div>
                        <div class="topic-icon">
                            <i class="fas fa-graduation-cap"></i>
                        </div>
                    </a>
                </div>
            </div>

            <!-- 热门标签 -->
            <div class="trending-section">
                <h2 class="section-title">
                    <i class="fas fa-hashtag"></i>
                    热门标签
                </h2>
                <div class="trending-tags">
                    <a href="#" class="trending-tag">
                        <i class="fas fa-fire fire"></i>
                        柴犬
                    </a>
                    <a href="#" class="trending-tag">
                        <i class="fas fa-fire fire"></i>
                        橘猫
                    </a>
                    <a href="#" class="trending-tag">
                        <i class="fas fa-fire fire"></i>
                        第一次洗澡
                    </a>
                    <a href="#" class="trending-tag">
                        <i class="fas fa-fire fire"></i>
                        疫苗接种
                    </a>
                    <a href="#" class="trending-tag">
                        <i class="fas fa-fire fire"></i>
                        公园遛狗
                    </a>
                    <a href="#" class="trending-tag">
                        <i class="fas fa-fire fire"></i>
                        宠物美容
                    </a>
                    <a href="#" class="trending-tag">
                        <i class="fas fa-fire fire"></i>
                        健康饮食
                    </a>
                    <a href="#" class="trending-tag">
                        <i class="fas fa-fire fire"></i>
                        同城交友
                    </a>
                </div>
            </div>
        </div>

        <!-- 创建话题按钮 -->
        <button class="create-topic-btn pulse">
            <i class="fas fa-plus"></i>
        </button>

        <!-- 底部导航栏 -->
        <div class="tab-bar">
            <a href="#" class="tab-item">
                <i class="fas fa-home"></i>
                <span>主页</span>
            </a>
            <a href="#" class="tab-item active">
                <i class="fas fa-hashtag"></i>
                <span>话题</span>
            </a>
            <a href="#" class="tab-item">
                <i class="fas fa-plus-circle"></i>
                <span>创作</span>
            </a>
            <a href="#" class="tab-item">
                <i class="fas fa-bell"></i>
                <span>消息</span>
            </a>
            <a href="#" class="tab-item">
                <i class="fas fa-user"></i>
                <span>我的</span>
            </a>
        </div>
    </div>

    <script>
        // 话题导航切换
        document.querySelectorAll('.topic-nav-item').forEach(item => {
            item.addEventListener('click', function () {
                document.querySelectorAll('.topic-nav-item').forEach(nav => nav.classList.remove('active'));
                this.classList.add('active');
            });
        });

        // 搜索功能
        document.querySelector('.search-input').addEventListener('focus', function () {
            this.style.borderColor = 'var(--primary-color)';
        });

        document.querySelector('.search-input').addEventListener('blur', function () {
            this.style.borderColor = 'var(--border-color)';
        });

        // 创建话题按钮动画
        document.querySelector('.create-topic-btn').addEventListener('click', function () {
            this.style.transform = 'scale(0.9)';
            setTimeout(() => {
                this.style.transform = 'scale(1.1)';
                setTimeout(() => {
                    this.style.transform = 'scale(1)';
                }, 100);
            }, 100);
        });

        // 话题项点击效果
        document.querySelectorAll('.topic-item').forEach(item => {
            item.addEventListener('click', function (e) {
                e.preventDefault();
                this.style.transform = 'scale(0.98)';
                setTimeout(() => {
                    this.style.transform = 'scale(1)';
                }, 150);
            });
        });
    </script>
</body>

</html>
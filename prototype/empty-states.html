<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>萌宠记 - 数据状态展示</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
    <style>
        /* 数据状态样式 */
        .state-container {
            padding: 20px;
            background: var(--background-secondary);
        }

        .state-section {
            background: var(--background);
            border-radius: var(--border-radius);
            margin-bottom: 20px;
            overflow: hidden;
        }

        .state-header {
            padding: 16px;
            border-bottom: 1px solid var(--border-color);
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
        }

        .state-content {
            padding: 40px 20px;
        }

        /* 空状态 */
        .empty-state {
            text-align: center;
            color: var(--text-secondary);
        }

        .empty-state .icon {
            font-size: 48px;
            margin-bottom: 16px;
            opacity: 0.5;
        }

        .empty-state h3 {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--text-primary);
        }

        .empty-state p {
            font-size: 14px;
            line-height: 1.4;
            margin-bottom: 20px;
        }

        .empty-state .btn {
            margin-top: 16px;
        }

        /* 加载状态 */
        .loading-state {
            text-align: center;
            padding: 40px 20px;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 3px solid var(--border-color);
            border-top: 3px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 16px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loading-text {
            font-size: 14px;
            color: var(--text-secondary);
        }

        /* 错误状态 */
        .error-state {
            text-align: center;
            color: var(--text-secondary);
        }

        .error-state .icon {
            font-size: 48px;
            margin-bottom: 16px;
            color: #e74c3c;
        }

        .error-state h3 {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--text-primary);
        }

        .error-state p {
            font-size: 14px;
            line-height: 1.4;
            margin-bottom: 20px;
        }

        .retry-btn {
            background: #e74c3c;
            color: white;
        }

        .retry-btn:hover {
            background: #c0392b;
        }

        /* 网络错误状态 */
        .network-error {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: var(--border-radius);
            padding: 16px;
            margin: 16px;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .network-error .icon {
            color: #f39c12;
            font-size: 20px;
        }

        .network-error .content {
            flex: 1;
        }

        .network-error .title {
            font-size: 14px;
            font-weight: 600;
            color: #8b4513;
            margin-bottom: 4px;
        }

        .network-error .desc {
            font-size: 12px;
            color: #8b4513;
        }

        .network-error .close-btn {
            background: none;
            border: none;
            color: #8b4513;
            font-size: 16px;
            cursor: pointer;
        }

        /* 骨架屏 */
        .skeleton {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
        }

        @keyframes loading {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }

        .skeleton-post {
            padding: 16px;
            border-bottom: 1px solid var(--border-color);
        }

        .skeleton-header {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
        }

        .skeleton-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin-right: 12px;
        }

        .skeleton-user {
            flex: 1;
        }

        .skeleton-name {
            height: 16px;
            width: 80px;
            border-radius: 4px;
            margin-bottom: 4px;
        }

        .skeleton-time {
            height: 12px;
            width: 60px;
            border-radius: 4px;
        }

        .skeleton-content {
            height: 16px;
            border-radius: 4px;
            margin-bottom: 8px;
        }

        .skeleton-content.short {
            width: 70%;
        }

        .skeleton-image {
            height: 200px;
            border-radius: var(--border-radius);
            margin-bottom: 12px;
        }

        .skeleton-actions {
            display: flex;
            gap: 20px;
        }

        .skeleton-action {
            height: 16px;
            width: 40px;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="iphone-container">
        <!-- iOS 状态栏 -->
        <div class="status-bar">
            <div class="status-left">
                <span>9:41</span>
            </div>
            <div class="status-right">
                <i class="fas fa-signal"></i>
                <i class="fas fa-wifi"></i>
                <div class="battery">
                    <div class="battery-fill"></div>
                </div>
            </div>
        </div>

        <!-- 主内容区域 -->
        <div class="main-content">
            <div class="state-container">
                <!-- 网络错误提示 -->
                <div class="network-error">
                    <i class="fas fa-wifi icon"></i>
                    <div class="content">
                        <div class="title">网络连接异常</div>
                        <div class="desc">请检查网络设置后重试</div>
                    </div>
                    <button class="close-btn">
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <!-- 空状态示例 -->
                <div class="state-section">
                    <div class="state-header">空状态 - 暂无内容</div>
                    <div class="state-content">
                        <div class="empty-state">
                            <div class="icon">
                                <i class="fas fa-paw"></i>
                            </div>
                            <h3>还没有宠物哦</h3>
                            <p>快来添加你的第一只毛孩子<br>开始记录美好时光吧！</p>
                            <button class="btn btn-primary">
                                <i class="fas fa-plus"></i>
                                添加宠物
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 加载状态示例 -->
                <div class="state-section">
                    <div class="state-header">加载状态</div>
                    <div class="state-content">
                        <div class="loading-state">
                            <div class="spinner"></div>
                            <div class="loading-text">正在加载内容...</div>
                        </div>
                    </div>
                </div>

                <!-- 错误状态示例 -->
                <div class="state-section">
                    <div class="state-header">错误状态</div>
                    <div class="state-content">
                        <div class="error-state">
                            <div class="icon">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <h3>加载失败</h3>
                            <p>网络连接异常，请检查网络后重试</p>
                            <button class="btn retry-btn">
                                <i class="fas fa-redo"></i>
                                重新加载
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 骨架屏示例 -->
                <div class="state-section">
                    <div class="state-header">骨架屏加载</div>
                    <div class="skeleton-post">
                        <div class="skeleton-header">
                            <div class="skeleton skeleton-avatar"></div>
                            <div class="skeleton-user">
                                <div class="skeleton skeleton-name"></div>
                                <div class="skeleton skeleton-time"></div>
                            </div>
                        </div>
                        <div class="skeleton skeleton-content"></div>
                        <div class="skeleton skeleton-content short"></div>
                        <div class="skeleton skeleton-image"></div>
                        <div class="skeleton-actions">
                            <div class="skeleton skeleton-action"></div>
                            <div class="skeleton skeleton-action"></div>
                            <div class="skeleton skeleton-action"></div>
                        </div>
                    </div>
                </div>

                <!-- 更多空状态示例 -->
                <div class="state-section">
                    <div class="state-header">搜索无结果</div>
                    <div class="state-content">
                        <div class="empty-state">
                            <div class="icon">
                                <i class="fas fa-search"></i>
                            </div>
                            <h3>没有找到相关内容</h3>
                            <p>试试其他关键词<br>或者浏览推荐内容</p>
                            <button class="btn btn-secondary">
                                <i class="fas fa-home"></i>
                                浏览推荐
                            </button>
                        </div>
                    </div>
                </div>

                <div class="state-section">
                    <div class="state-header">消息为空</div>
                    <div class="state-content">
                        <div class="empty-state">
                            <div class="icon">
                                <i class="fas fa-bell"></i>
                            </div>
                            <h3>暂无新消息</h3>
                            <p>当有人给你点赞、评论或关注时<br>消息会显示在这里</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部导航栏 -->
        <div class="tab-bar">
            <a href="home.html" class="tab-item">
                <i class="fas fa-home"></i>
                <span>主页</span>
            </a>
            <a href="topics.html" class="tab-item">
                <i class="fas fa-hashtag"></i>
                <span>话题</span>
            </a>
            <a href="create.html" class="tab-item">
                <i class="fas fa-plus-circle"></i>
                <span>创作</span>
            </a>
            <a href="messages.html" class="tab-item">
                <i class="fas fa-bell"></i>
                <span>消息</span>
            </a>
            <a href="profile.html" class="tab-item active">
                <i class="fas fa-user"></i>
                <span>我的</span>
            </a>
        </div>
    </div>

    <script>
        // 关闭网络错误提示
        document.querySelector('.close-btn').addEventListener('click', function() {
            this.closest('.network-error').style.display = 'none';
        });

        // 重试按钮
        document.querySelector('.retry-btn').addEventListener('click', function() {
            const errorState = this.closest('.error-state');
            errorState.innerHTML = `
                <div class="loading-state">
                    <div class="spinner"></div>
                    <div class="loading-text">正在重新加载...</div>
                </div>
            `;
            
            // 模拟重新加载
            setTimeout(() => {
                errorState.innerHTML = `
                    <div class="empty-state">
                        <div class="icon">
                            <i class="fas fa-check-circle" style="color: #27ae60;"></i>
                        </div>
                        <h3>加载成功</h3>
                        <p>内容已成功加载</p>
                    </div>
                `;
            }, 2000);
        });

        // 添加宠物按钮
        document.querySelector('.btn-primary').addEventListener('click', function() {
            window.location.href = 'add-pet.html';
        });

        // 浏览推荐按钮
        document.querySelector('.btn-secondary').addEventListener('click', function() {
            window.location.href = 'home.html';
        });
    </script>
</body>
</html>

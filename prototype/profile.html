<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>萌宠记 - 个人中心</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
    <style>
        /* 个人中心特定样式 */
        .profile-header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 20px 16px;
            position: relative;
            overflow: hidden;
        }

        .profile-header::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200px;
            height: 200px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
        }

        .profile-info {
            display: flex;
            align-items: center;
            gap: 16px;
            margin-bottom: 20px;
            position: relative;
            z-index: 1;
        }

        .profile-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            border: 3px solid rgba(255, 255, 255, 0.3);
            object-fit: cover;
        }

        .profile-details h2 {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .profile-details p {
            font-size: 14px;
            opacity: 0.9;
            margin-bottom: 8px;
        }

        .level-badge {
            background: rgba(255, 255, 255, 0.2);
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            gap: 4px;
        }

        .stats-row {
            display: flex;
            justify-content: space-around;
            position: relative;
            z-index: 1;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 12px;
            opacity: 0.9;
        }

        .profile-content {
            padding: 0 16px;
        }

        .section {
            margin-bottom: 24px;
        }

        .section-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 16px;
        }

        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .section-action {
            color: var(--primary-color);
            font-size: 14px;
            font-weight: 500;
            text-decoration: none;
        }

        .pets-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
        }

        .pet-card {
            background: var(--background);
            border-radius: 16px;
            padding: 16px;
            text-align: center;
            box-shadow: var(--shadow);
            border: 1px solid var(--border-color);
            cursor: pointer;
            transition: all 0.2s;
        }

        .pet-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        }

        .pet-card img {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            object-fit: cover;
            margin-bottom: 12px;
        }

        .pet-name {
            font-size: 14px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 4px;
        }

        .pet-breed {
            font-size: 12px;
            color: var(--text-secondary);
            margin-bottom: 8px;
        }

        .pet-flowers {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
            font-size: 11px;
            color: var(--accent-color);
            font-weight: 500;
        }

        .add-pet-card {
            background: var(--background-secondary);
            border: 2px dashed var(--border-color);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 8px;
            color: var(--text-secondary);
        }

        .add-pet-card i {
            font-size: 24px;
        }

        .achievements {
            background: var(--background);
            border-radius: 16px;
            padding: 16px;
            box-shadow: var(--shadow);
        }

        .level-progress {
            margin-bottom: 16px;
        }

        .level-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .current-level {
            font-size: 14px;
            font-weight: 600;
            color: var(--primary-color);
        }

        .next-level {
            font-size: 12px;
            color: var(--text-secondary);
        }

        .progress-bar {
            height: 8px;
            background: var(--background-secondary);
            border-radius: 4px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
            width: 65%;
            border-radius: 4px;
        }

        .badges-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 12px;
        }

        .badge-item {
            text-align: center;
            padding: 12px 8px;
            background: var(--background-secondary);
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .badge-item.earned {
            background: linear-gradient(135deg, var(--accent-color), #FFFDE7);
            color: var(--text-primary);
        }

        .badge-item:hover {
            transform: scale(1.05);
        }

        .badge-icon {
            font-size: 20px;
            margin-bottom: 4px;
        }

        .badge-name {
            font-size: 10px;
            font-weight: 500;
        }

        .menu-list {
            background: var(--background);
            border-radius: 16px;
            overflow: hidden;
            box-shadow: var(--shadow);
        }

        .menu-item {
            display: flex;
            align-items: center;
            padding: 16px;
            border-bottom: 1px solid var(--border-color);
            color: var(--text-primary);
            text-decoration: none;
            transition: background 0.2s;
        }

        .menu-item:last-child {
            border-bottom: none;
        }

        .menu-item:hover {
            background: var(--background-secondary);
        }

        .menu-icon {
            width: 24px;
            text-align: center;
            margin-right: 12px;
            color: var(--primary-color);
        }

        .menu-text {
            flex: 1;
            font-size: 15px;
            font-weight: 500;
        }

        .menu-arrow {
            color: var(--text-secondary);
            font-size: 12px;
        }

        .menu-badge {
            background: var(--primary-color);
            color: white;
            border-radius: 10px;
            padding: 2px 6px;
            font-size: 10px;
            font-weight: 600;
            margin-right: 8px;
        }
    </style>
</head>

<body>
    <div class="iphone-container">
        <!-- iOS 状态栏 -->
        <div class="status-bar">
            <div class="status-left">
                <span>9:41</span>
            </div>
            <div class="status-right">
                <i class="fas fa-signal"></i>
                <i class="fas fa-wifi"></i>
                <div class="battery">
                    <div class="battery-fill"></div>
                </div>
            </div>
        </div>

        <!-- 主内容区域 -->
        <div class="main-content">
            <!-- 个人资料头部 -->
            <div class="profile-header">
                <button class="settings-btn" onclick="window.location.href='settings.html'"
                    style="position: absolute; top: 16px; right: 16px; background: none; border: none; font-size: 20px; color: white; cursor: pointer; z-index: 10;">
                    <i class="fas fa-cog"></i>
                </button>
                <div class="profile-info">
                    <img src="https://images.unsplash.com/photo-1494790108755-2616c9c9b8d4?w=160&h=160&fit=crop&crop=face"
                        alt="用户头像" class="profile-avatar">
                    <div class="profile-details">
                        <h2>萌宠小雅</h2>
                        <p>记录毛孩子的每一个美好瞬间 🐾</p>
                        <div class="level-badge">
                            <i class="fas fa-star"></i>
                            Lv.3 活跃达人
                        </div>
                    </div>
                </div>

                <div class="stats-row">
                    <div class="stat-item">
                        <div class="stat-number">128</div>
                        <div class="stat-label">动态</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">1.2k</div>
                        <div class="stat-label">关注</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">3.5k</div>
                        <div class="stat-label">粉丝</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">256</div>
                        <div class="stat-label">获赞</div>
                    </div>
                </div>
            </div>

            <div class="profile-content">
                <!-- 我的宠物 -->
                <div class="section">
                    <div class="section-header">
                        <h3 class="section-title">
                            <i class="fas fa-paw"></i>
                            我的宠物
                        </h3>
                        <a href="add-pet.html" class="section-action">添加</a>
                    </div>
                    <div class="pets-grid">
                        <div class="pet-card hover-lift" onclick="window.location.href='pet-profile.html'"
                            style="cursor: pointer;">
                            <img src="https://images.unsplash.com/photo-1552053831-71594a27632d?w=120&h=120&fit=crop&crop=face"
                                alt="球球">
                            <div class="pet-name">球球</div>
                            <div class="pet-breed">金毛寻回犬 · 2岁</div>
                            <div class="pet-flowers">
                                <i class="fas fa-seedling"></i>
                                <span>今日收花 5</span>
                            </div>
                        </div>
                        <div class="pet-card">
                            <img src="https://images.unsplash.com/photo-1574158622682-e40e69881006?w=120&h=120&fit=crop&crop=face"
                                alt="小橘">
                            <div class="pet-name">小橘</div>
                            <div class="pet-breed">橘猫 · 1岁</div>
                            <div class="pet-flowers">
                                <i class="fas fa-seedling"></i>
                                <span>今日收花 3</span>
                            </div>
                        </div>
                        <div class="pet-card add-pet-card">
                            <i class="fas fa-plus"></i>
                            <span>添加宠物</span>
                        </div>
                    </div>
                </div>

                <!-- 成长体系 -->
                <div class="section">
                    <div class="section-header">
                        <h3 class="section-title">
                            <i class="fas fa-trophy"></i>
                            成长体系
                        </h3>
                        <a href="#" class="section-action">详情</a>
                    </div>
                    <div class="achievements">
                        <div class="level-progress">
                            <div class="level-info">
                                <span class="current-level">Lv.3 活跃达人</span>
                                <span class="next-level">距离Lv.4还需320积分</span>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill"></div>
                            </div>
                        </div>

                        <div class="badges-grid">
                            <div class="badge-item earned">
                                <div class="badge-icon">🏆</div>
                                <div class="badge-name">新手</div>
                            </div>
                            <div class="badge-item earned">
                                <div class="badge-icon">⭐</div>
                                <div class="badge-name">活跃</div>
                            </div>
                            <div class="badge-item earned">
                                <div class="badge-icon">📸</div>
                                <div class="badge-name">摄影师</div>
                            </div>
                            <div class="badge-item">
                                <div class="badge-icon">👑</div>
                                <div class="badge-name">达人</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 功能菜单 -->
                <div class="section">
                    <div class="menu-list">
                        <a href="#" class="menu-item">
                            <i class="fas fa-bookmark menu-icon"></i>
                            <span class="menu-text">我的收藏</span>
                            <i class="fas fa-chevron-right menu-arrow"></i>
                        </a>
                        <a href="#" class="menu-item">
                            <i class="fas fa-clock menu-icon"></i>
                            <span class="menu-text">稍后观看</span>
                            <span class="menu-badge">3</span>
                            <i class="fas fa-chevron-right menu-arrow"></i>
                        </a>
                        <a href="#" class="menu-item">
                            <i class="fas fa-edit menu-icon"></i>
                            <span class="menu-text">草稿箱</span>
                            <span class="menu-badge">2</span>
                            <i class="fas fa-chevron-right menu-arrow"></i>
                        </a>
                        <a href="#" class="menu-item">
                            <i class="fas fa-chart-line menu-icon"></i>
                            <span class="menu-text">数据统计</span>
                            <i class="fas fa-chevron-right menu-arrow"></i>
                        </a>
                        <a href="#" class="menu-item">
                            <i class="fas fa-users menu-icon"></i>
                            <span class="menu-text">我的关注</span>
                            <i class="fas fa-chevron-right menu-arrow"></i>
                        </a>
                        <a href="#" class="menu-item">
                            <i class="fas fa-cog menu-icon"></i>
                            <span class="menu-text">设置</span>
                            <i class="fas fa-chevron-right menu-arrow"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部导航栏 -->
        <div class="tab-bar">
            <a href="#" class="tab-item">
                <i class="fas fa-home"></i>
                <span>主页</span>
            </a>
            <a href="#" class="tab-item">
                <i class="fas fa-hashtag"></i>
                <span>话题</span>
            </a>
            <a href="#" class="tab-item">
                <i class="fas fa-plus-circle"></i>
                <span>创作</span>
            </a>
            <a href="#" class="tab-item">
                <i class="fas fa-bell"></i>
                <span>消息</span>
            </a>
            <a href="#" class="tab-item active">
                <i class="fas fa-user"></i>
                <span>我的</span>
            </a>
        </div>
    </div>

    <script>
        // 宠物卡片点击效果
        document.querySelectorAll('.pet-card').forEach(card => {
            card.addEventListener('click', function () {
                if (!this.classList.contains('add-pet-card')) {
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = 'scale(1)';
                    }, 150);
                }
            });
        });

        // 徽章点击效果
        document.querySelectorAll('.badge-item').forEach(badge => {
            badge.addEventListener('click', function () {
                this.style.transform = 'scale(1.1)';
                setTimeout(() => {
                    this.style.transform = 'scale(1)';
                }, 200);
            });
        });

        // 菜单项点击效果
        document.querySelectorAll('.menu-item').forEach(item => {
            item.addEventListener('click', function (e) {
                e.preventDefault();
                this.style.backgroundColor = 'var(--background-secondary)';
                setTimeout(() => {
                    this.style.backgroundColor = '';
                }, 200);
            });
        });

        // 添加宠物卡片点击
        document.querySelector('.add-pet-card').addEventListener('click', function () {
            this.style.transform = 'scale(0.95)';
            this.style.borderColor = 'var(--primary-color)';
            setTimeout(() => {
                this.style.transform = 'scale(1)';
                this.style.borderColor = 'var(--border-color)';
            }, 200);
        });

        // 统计数据点击效果
        document.querySelectorAll('.stat-item').forEach(stat => {
            stat.addEventListener('click', function () {
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = 'scale(1)';
                }, 150);
            });
        });
    </script>
</body>

</html>
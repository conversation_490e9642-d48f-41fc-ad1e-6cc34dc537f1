<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>萌宠记 - 个人中心</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
    <style>
        /* 个人中心特定样式 */
        .profile-header {
            position: relative;
            padding-top: 20px;
            margin-bottom: 20px;
        }

        .header-background {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 130px;
            background: linear-gradient(45deg, var(--primary), #9932cc);
            border-radius: 0 0 30px 30px;
            z-index: 1;
        }

        .header-content {
            position: relative;
            z-index: 2;
            padding: 0 20px;
            padding-bottom: 15px;
        }

        .avatar-container {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            padding: 0 15px;
        }

        .avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            border: 3px solid white;
            object-fit: cover;
            background: #fff;
            margin-right: 15px;
        }

        .user-info {
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .username {
            color: #ffffff;
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .user-id {
            color: #cccccc;
            font-size: 14px;
        }

        .profile-details h2 {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .profile-details p {
            font-size: 14px;
            opacity: 0.9;
            margin-bottom: 8px;
        }

        .level-badge {
            background: rgba(255, 255, 255, 0.2);
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            gap: 4px;
        }

        .stats-row {
            display: flex;
            justify-content: space-around;
            position: relative;
            z-index: 1;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 12px;
            opacity: 0.9;
        }

        .profile-content {
            padding: 0 16px;
        }

        .section {
            margin-bottom: 24px;
        }

        .section-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 16px;
        }

        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .section-action {
            color: var(--primary-color);
            font-size: 14px;
            font-weight: 500;
            text-decoration: none;
        }

        .pets-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
        }

        .pet-card {
            background: var(--background);
            border-radius: 16px;
            padding: 16px;
            text-align: center;
            box-shadow: var(--shadow);
            border: 1px solid var(--border-color);
            cursor: pointer;
            transition: all 0.2s;
        }

        .pet-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        }

        .pet-card img {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            object-fit: cover;
            margin-bottom: 12px;
        }

        .pet-name {
            font-size: 14px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 4px;
        }

        .pet-breed {
            font-size: 12px;
            color: var(--text-secondary);
            margin-bottom: 8px;
        }

        .pet-flowers {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
            font-size: 11px;
            color: var(--accent-color);
            font-weight: 500;
        }

        .add-pet-card {
            background: var(--background-secondary);
            border: 2px dashed var(--border-color);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 8px;
            color: var(--text-secondary);
        }

        .add-pet-card i {
            font-size: 24px;
        }

        .achievements {
            background: var(--background);
            border-radius: 16px;
            padding: 16px;
            box-shadow: var(--shadow);
        }

        .level-progress {
            margin-bottom: 16px;
        }

        .level-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .current-level {
            font-size: 14px;
            font-weight: 600;
            color: var(--primary-color);
        }

        .next-level {
            font-size: 12px;
            color: var(--text-secondary);
        }

        .progress-bar {
            height: 8px;
            background: var(--background-secondary);
            border-radius: 4px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
            width: 65%;
            border-radius: 4px;
        }

        .badges-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 12px;
        }

        .badge-item {
            text-align: center;
            padding: 12px 8px;
            background: var(--background-secondary);
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .badge-item.earned {
            background: linear-gradient(135deg, var(--accent-color), #FFFDE7);
            color: var(--text-primary);
        }

        .badge-item:hover {
            transform: scale(1.05);
        }

        .badge-icon {
            font-size: 20px;
            margin-bottom: 4px;
        }

        .badge-name {
            font-size: 10px;
            font-weight: 500;
        }

        .menu-list {
            padding: 0 20px;
            margin-bottom: 30px;
            margin-top: 5px;
        }

        .menu-item {
            display: flex;
            align-items: center;
            padding: 15px;
            background: white;
            border-radius: 12px;
            margin-bottom: 10px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
            text-decoration: none;
            color: var(--text-primary);
            transition: all 0.2s;
        }

        .menu-item:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .menu-icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            color: white;
            font-size: 18px;
        }

        .menu-icon.pets {
            background: linear-gradient(135deg, #9333EA, #6B21A8);
        }

        .menu-icon.achievements {
            background: linear-gradient(135deg, #10B981, #059669);
        }

        .menu-icon.bookmarks {
            background: linear-gradient(135deg, #F59E0B, #D97706);
        }

        .menu-icon.drafts {
            background: linear-gradient(135deg, #3B82F6, #2563EB);
        }

        .menu-icon.stats {
            background: linear-gradient(135deg, #EC4899, #DB2777);
        }

        .menu-icon.settings {
            background: linear-gradient(135deg, #8B5CF6, #7C3AED);
        }

        .menu-text {
            flex: 1;
        }

        .menu-title {
            font-size: 16px;
            font-weight: 500;
            color: var(--gray-800);
            margin-bottom: 3px;
        }

        .menu-desc {
            font-size: 13px;
            color: var(--gray-500);
        }

        .menu-arrow {
            color: var(--gray-400);
        }

        .menu-badge {
            background: var(--primary);
            color: white;
            border-radius: 10px;
            padding: 2px 6px;
            font-size: 10px;
            font-weight: 600;
            margin-right: 8px;
        }
    </style>
</head>

<body>
    <div class="iphone-container">
        <!-- iOS 状态栏 -->
        <div class="status-bar">
            <div class="status-bar-notch"></div>
            <div class="status-bar-left">
                <span>中国移动</span>
                <i class="fas fa-signal"></i>
            </div>
            <div class="status-bar-center status-bar-time">9:41</div>
            <div class="status-bar-right">
                <i class="fas fa-wifi"></i>
                <i class="fas fa-battery-three-quarters"></i>
            </div>
        </div>

        <!-- 主内容区域 -->
        <div class="main-content">
            <!-- 个人资料头部 -->
            <div class="profile-header">
                <div class="header-background"></div>
                <div class="header-content">
                    <div class="avatar-container">
                        <img src="https://images.unsplash.com/photo-1494790108755-2616c9c9b8d4?w=160&h=160&fit=crop&crop=face"
                            alt="用户头像" class="avatar">
                        <div class="user-info">
                            <h1 class="username">萌宠小雅</h1>
                            <div class="user-id">ID: 2023765</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="menu-list">
                <div class="menu-item" onclick="window.location.href='pet-profile.html'">
                    <div class="menu-icon pets">
                        <i class="fas fa-paw"></i>
                    </div>
                    <div class="menu-text">
                        <div class="menu-title">我的宠物</div>
                        <div class="menu-desc">管理您的萌宠档案</div>
                    </div>
                    <div class="menu-arrow">
                        <i class="fas fa-chevron-right"></i>
                    </div>
                </div>

                <div class="menu-item" onclick="window.location.href='#'">
                    <div class="menu-icon achievements">
                        <i class="fas fa-trophy"></i>
                    </div>
                    <div class="menu-text">
                        <div class="menu-title">成长体系</div>
                        <div class="menu-desc">查看等级和成就徽章</div>
                    </div>
                    <div class="menu-arrow">
                        <i class="fas fa-chevron-right"></i>
                    </div>
                </div>

                <div class="menu-item" onclick="window.location.href='#'">
                    <div class="menu-icon bookmarks">
                        <i class="fas fa-bookmark"></i>
                    </div>
                    <div class="menu-text">
                        <div class="menu-title">我的收藏</div>
                        <div class="menu-desc">收藏的精彩内容</div>
                    </div>
                    <div class="menu-arrow">
                        <i class="fas fa-chevron-right"></i>
                    </div>
                </div>

                <div class="menu-item" onclick="window.location.href='#'">
                    <div class="menu-icon drafts">
                        <i class="fas fa-edit"></i>
                    </div>
                    <div class="menu-text">
                        <div class="menu-title">草稿箱</div>
                        <div class="menu-desc">未发布的内容草稿</div>
                    </div>
                    <span class="menu-badge">2</span>
                    <div class="menu-arrow">
                        <i class="fas fa-chevron-right"></i>
                    </div>
                </div>

                <div class="menu-item" onclick="window.location.href='#'">
                    <div class="menu-icon stats">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="menu-text">
                        <div class="menu-title">数据统计</div>
                        <div class="menu-desc">查看互动数据分析</div>
                    </div>
                    <div class="menu-arrow">
                        <i class="fas fa-chevron-right"></i>
                    </div>
                </div>

                <div class="menu-item" onclick="window.location.href='settings.html'">
                    <div class="menu-icon settings">
                        <i class="fas fa-cog"></i>
                    </div>
                    <div class="menu-text">
                        <div class="menu-title">设置</div>
                        <div class="menu-desc">账号和隐私设置</div>
                    </div>
                    <div class="menu-arrow">
                        <i class="fas fa-chevron-right"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部导航栏 -->
        <div class="tab-bar">
            <a href="#" class="tab-item">
                <i class="fas fa-home"></i>
                <span>主页</span>
            </a>
            <a href="#" class="tab-item">
                <i class="fas fa-hashtag"></i>
                <span>话题</span>
            </a>
            <a href="#" class="tab-item">
                <i class="fas fa-plus-circle"></i>
                <span>创作</span>
            </a>
            <a href="#" class="tab-item">
                <i class="fas fa-bell"></i>
                <span>消息</span>
            </a>
            <a href="#" class="tab-item active">
                <i class="fas fa-user"></i>
                <span>我的</span>
            </a>
        </div>
    </div>

    <script>
        // 菜单项点击效果
        document.querySelectorAll('.menu-item').forEach(item => {
            item.addEventListener('click', function () {
                // 如果有特定的点击处理函数，不执行默认提示
                if (!this.hasAttribute('onclick')) {
                    alert('此功能将在后续版本中开放');
                }
            });
        });

        // 添加点击动画效果
        document.querySelectorAll('.menu-item').forEach(item => {
            item.addEventListener('mousedown', function () {
                this.style.transform = 'scale(0.98)';
            });

            item.addEventListener('mouseup', function () {
                this.style.transform = 'scale(1)';
            });

            item.addEventListener('mouseleave', function () {
                this.style.transform = 'scale(1)';
            });
        });
    </script>
</body>

</html>